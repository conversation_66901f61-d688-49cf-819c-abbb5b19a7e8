import Cookie from "@/utils/cookie.js"
import thinkingdata from '@/utils/ta_js_sdk/thinkingdata.umd.min';
import * as channelHelp from './channelHelp.js'
const log = console.log.bind(console)
export const getUrlParams = (name) => {
    const parsedUrl = new URL(window.location.href)
    return parsedUrl.searchParams.get(name)
}
export class ChannelChange {
    static currentPixelId = null // 当前
    static historyPixelId = []   // 历史
    // 默认 pixel id
    static defaultFacebocePixelId = `${GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID}` // facebook 默认 pixel id
    static defaultTiktokPixelId = `${GloablEnvConfig.DEFAULT_TIKTOK_PIXELID}` // tiktok 默认 pixel id
    static defaultSnapchatPixelId = `${GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID}` // snapchat 默认 pixel id
    static defaultPinterestPixelId = `${GloablEnvConfig.DEFAULT_PINTEREST_PIXELID}` // pinterest 默认 pixel id
    // js 引入状态
    static facebookImportStatus = false
    static tiktokImportStatus = false
    static googleImportStatus = false
    static pinterestImportStatus = false
    // 当前使用的渠道
    static isFacebook = false
    static isGoogle = false
    static isTiktok = false
    static isSnapchat = false;
    static isPinterest = false;
    // 渠道名 
    static currentSource = null
    static currentChannelKey = null
    //
    static tiktokResult = null // tiktok 变量资源，用于埋点和初始化相关
    static facebookResult = null // facebook 变量资源，用于埋点和初始化相关
    static snapchatResult = null // snapchat 变量资源，用于埋点和初始化相关
    static pinterestResult = null // pinterest 变量资源，用于埋点和初始化相关
    // facebook init 客户信息参数 初始化
    // https://o6vznlro9a.feishu.cn/docx/S0n2dVDT8oj5JbxYa83ctAsandy 6. 优化W2A付费属性事件上报
    // https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/customer-information-parameters
    static facebookExternalId = null // facebook 用户id
    static snapchatExternalId = null // snapchat 用户id
    static pinterestExternalId = null // pinterest 用户id

    //
    static channelMaps = {
        facebook: {
            key: 'facebook',
            text: '【 facebook 脸书】',
        },
        tiktok: {
            key: 'tiktok',
            text: '【 tiktok 抖音】',
        },
        google: {
            key: 'google',
            text: '【 google 谷歌】',
        },
        snapchat: {
            key: 'snapchat',
            text: '【 Snapchat 快照】',
        },
        pinterest: {
            key: 'pinterest',
            text: '【 Pinterest 拼趣】',
        }
    }
    static channelUrlParamKeys = {
        facebook: 'facebook_ip_int',
        tiktok: 'tiktok_ip_int',
        snapchat: 'snapchat_ip_int',
        google: 'google_ip_int',
        pinterest: 'pinterest_ip_int',
    }

    static titkokGetCookie(name) {
        let cookie = {};
        document.cookie.split(';').forEach(function(el) {
            let [k,v] = el.split('=')
            cookie[k.trim()] = v
        })
        return cookie[name]
    }

    // 统一的Cookie获取方法
    static getCookie(name) {
        return Cookie.get(name)
    }

    // Facebook相关Cookie获取方法
    static getFacebookCookies() {
        return {
            fbp: Cookie.get("_fbp"),
            fbc: Cookie.get("_fbc")
        }
    }

    // TikTok相关Cookie获取方法
    static getTikTokCookies() {
        return {
            ttp: Cookie.get("_ttp")
        }
    }

    // Snapchat相关Cookie获取方法
    static getSnapchatCookies() {
        return {
            scid: Cookie.get("_scid")
        }
    }

    // 从 Pinterest _epik cookie 中提取点击ID
    static getPinterestClickId() {
        // https://developers.pinterest.com/docs/api-features/track-conversion-events/
        let epikCookie = Cookie.get("_epik")
        if (epikCookie) {
            // _epik cookie 格式通常包含点击ID信息
            // 根据Pinterest文档，_epik包含用户ID和点击信息
            return epikCookie
        }
        return null
    }

    // Pinterest相关Cookie获取方法
    static getPinterestCookies() {
        return {
            epik: this.getPinterestClickId()
        }
    }

    // 检查平台tracking cookies是否存在
    static checkTrackingCookies() {
        if (this.isFacebook) {
            const cookies = this.getFacebookCookies()
            return cookies.fbp && cookies.fbc
        } else if (this.isTiktok) {
            const cookies = this.getTikTokCookies()
            return cookies.ttp
        } else if (this.isPinterest) {
            return this.getPinterestClickId()
        } else if (this.isSnapchat) {
            const cookies = this.getSnapchatCookies()
            return cookies.scid
        }
        return false
    }

    // 获取当前平台的cookie信息用于日志
    static getCurrentPlatformCookiesLog() {
        if (this.isFacebook) {
            const cookies = this.getFacebookCookies()
            return `_fbp: ${cookies.fbp}, _fbc: ${cookies.fbc}`
        } else if (this.isTiktok) {
            const cookies = this.getTikTokCookies()
            return `_ttp: ${cookies.ttp}`
        } else if (this.isPinterest) {
            return `_epik: ${this.getPinterestClickId()}`
        } else if (this.isSnapchat) {
            const cookies = this.getSnapchatCookies()
            return `_scid: ${cookies.scid}`
        }
        return ''
    }

    // 获取当前平台名称
    static getCurrentPlatformName() {
        if (this.isFacebook) {
            return channelMaps.facebook.text
        } else if (this.isTiktok) {
             return channelMaps.tiktok.text
        } else if (this.isPinterest) {
            return channelMaps.pinterest.text
        } else if (this.isSnapchat) {
            return channelMaps.snapchat.text
        } else if (this.isGoogle) {
            return channelMaps.google.text
        }
        return 'Unknown'
    }
    static tiktokTrackMap = new Map([
        ['ViewContent', 'ViewContent'],// 查看内容 当访客浏览特定页面时。TikTok建议监测影响业务的关键页面，比如产品比较页面、官方动态页面或产品发布页面等。
        ['ClickButton', 'ClickButton'],// 落地页用户点击下载按钮时回传；（前端通过piexl回传）
        ['ViewContentFinish', 'ViewContentFinish'],// 落地页用户滚动到底部回传；（前端通过piexl回传）
    ])
    static facebookTrackMap = new Map([
        ['PageView', 'PageView'],// 查看内容
        // ['PageContentFinish', 'PageContentFinish'],// 落地页用户滚动到底部回传；（前端通过piexl回传）
    ])
    // Snapchat 事件映射表
    static snapchatTrackMap = new Map([
         /*
            事件文档集合
            https://businesshelp.snapchat.com/s/article/event-file-requirements?language=zh_CN
        */
        ['PAGE_VIEW', 'PAGE_VIEW'], // 页面加载时触发 https://businesshelp.snapchat.com/s/article/page-view-event?language=zh_CN
        ['AD_CLICK', 'AD_CLICK'], // 用户点击广告时触发
        ['AD_VIEW', 'AD_VIEW'], // 用户滚动到底部时触发
    ]);
    // Pinterest 事件映射表
    static pinterestTrackMap = new Map([
         /*
            事件文档集合
            https://help.pinterest.com/zh-hans/business/article/add-event-codes
        */
        ['ViewContent', 'ViewContent'], // 跟踪查看你的网页（例如产品页面或着陆页）的用户
        ['ClickButton', 'ClickButton'], // 落地页用户点击下载按钮时回传
        ['ViewContentFinish', 'ViewContentFinish'], // 落地页用户滚动到底部回传
    ]);
    constructor() {
    }
    static currentText() {
        let lText = `【 未定义 】`
        let e = this.channelMaps[this.currentChannelKey]
        // console.log("🚀 ~ currentText ~ e:", e, this.currentChannelKey)
        return e.text || lText
    }
    static trackEvent(eventName, eventData = {},) {
        if (!eventName) {
            console.error(`ChannelChange: 埋点 调用失败 未传递事件名: ${eventName} 事件参数: ${eventData} 当前渠道 ${this.getSource()}`,)
            return
        }


        if (this.isFacebook && this.facebookResult) {
            // 不知道能不能传递参数，到时候需要再加吧
            this.facebookResult('track', eventName)
        } else if (this.isTiktok && this.tiktokResult) {
            // https://o6vznlro9a.feishu.cn/sheets/VvPysFjeqhnBgDtArGHcTA20nig?sheet=f41646&range=QTEyOkExNA
            this.tiktokResult.track(eventName, eventData)
        } else if (this.isSnapchat && this.snapchatResult) {
            // https://businesshelp.snapchat.com/s/article/view-content-event?language=zh_CN
            this.snapchatResult('track', eventName, eventData);
        } else if (this.isPinterest && this.pinterestResult) {
            // https://help.pinterest.com/zh-hans/business/article/add-event-codes
            this.pinterestResult('track', eventName, eventData);
        }
        else {
            console.error("ChannelChange: 埋点 调用失败 未识别到渠道")
            return
        }

        log(`ChannelChange: ${this.currentText()}埋点 eventName: ${eventName} eventData: ${JSON.stringify(eventData)}`)
    }
    static facebookImportJs() {
        // require("@/utils/onelink-smart-script-latest")
        try {
            // ！！ 注意这里魔法字符串不能带前缀的 @ 因为会解析不到
            require(`@/${GloablEnvConfig.FACEBOOK_FILENAME}`)
            this.facebookImportStatus = true
        } catch (error) {
            console.error(`🚀 ~ ${this.currentText()} 引入 js 失败，文件不存在 `, error)
        }
        log(`ChannelChange: ${this.currentText()} JS 创建  调用成功`)
    }
    static tiktokImportJs() {
        // require("@/utils/onelink-smart-script-latest")
        try {
            // ！！ 注意这里魔法字符串不能带前缀的 @ 因为会解析不到
            require(`@/${GloablEnvConfig.TIKTOK_FILENAME}`)
            this.tiktokImportStatus = true
        } catch (error) {
            console.error(`🚀 ~ ${this.currentText()} 引入 归因 af js 失败，文件不存在 `, error)
        }
        log(`ChannelChange: ${this.currentText()} JS 创建  调用成功`)
    }
    static snapchatImportJs() {
        try {
            require(`@/${GloablEnvConfig.SNAPCHAT_FILENAME}`)
            this.snapchatImportStatus = true
        } catch (error) {
            console.error(`🚀 ~ ${this.currentText()} 引入 js 失败，文件不存在 `, error)
        }
        log(`ChannelChange: ${this.currentText()} JS 创建  调用成功`)
    }
    static pinterestImportJs() {
        try {
            require(`@/${GloablEnvConfig.PINTEREST_FILENAME}`)
            this.pinterestImportStatus = true
        } catch (error) {
            console.error(`🚀 ~ ${this.currentText()} 引入 js 失败，文件不存在 `, error)
        }
        log(`ChannelChange: ${this.currentText()} JS 创建  调用成功`)
    }
    static googleImportJs() {
        // TODO: 区分产品
        require("@/utils/googleAds.js")
        this.googleImportStatus = true
        log(`ChannelChange: ${this.currentText()} JS 创建  调用成功`)
    }
    static googleImportGtagJs(callback) {
        let linkSrc = `https://www.googletagmanager.com/gtag/js?id=${window.GloablEnvConfig.gtagJSinitId}`
        // 创建一个 <script> 元素
        let scriptElement = document.createElement('script')
        // 设置要加载的脚本文件的 URL
        scriptElement.src = linkSrc
        // 可选：指定脚本的类型，例如 'text/javascript'
        scriptElement.type = 'text/javascript'
        // 可选：为脚本指定异步加载属性
        scriptElement.async = true // async|defer 前者不保证加载顺序执行，后者按照正常顺序执行
        // 当脚本加载完成时执行的回调函数
        scriptElement.onload = () => {
            log("ChannelChange: gtagjs JS 外部脚本已加载  调用成功")
            callback()
        }
        // 将 <script> 元素添加到文档的 <head> 或 <body> 中
        document.head.appendChild(scriptElement)
    }
    static googleGtagIdInit() {
        window.dataLayer = window.dataLayer || []
        function gtag(){
            dataLayer.push(arguments)
        }
        gtag('js', new Date())
        gtag('config', window.GloablEnvConfig.gtagJSinitId)
    }
    // 自动根据渠道引用js
    static autoImportChanelJs(pixelId) {
        if (this.isFacebook) {
            this.facebookImportJs()
            let id = pixelId
            if (!id) {
                id = this.defaultFacebocePixelId
            }
            this.publicPixedlIdInit(id)
        } else if (this.isTiktok) {
            this.tiktokImportJs()
            let id = pixelId
            if (!id) {
                id = this.defaultTiktokPixelId
            }
            this.publicPixedlIdInit(id)
        } else if (this.isSnapchat) {
            this.snapchatImportJs()
            let id = pixelId
            if (!id) {
                id = this.defaultSnapchatPixelId
            }
            this.publicPixedlIdInit(id)
        } else if (this.isPinterest) {
            this.pinterestImportJs()
            let id = pixelId
            if (!id) {
                id = this.defaultPinterestPixelId
            }
            this.publicPixedlIdInit(id)
        } else if (this.isGoogle) {
            this.googleImportJs()
            this.googleImportGtagJs(() => {
                this.googleGtagIdInit()
            })
        }
        else {
            alert('autoImportChanelJs 未识别到渠道')
        }
    }

    static facebookPixedlIdInit() {
        !(function (f, b, e, v, n, t, s) {
            if (f.fbq) return
            n = f.fbq = function () {
                n.callMethod
                    ? n.callMethod.apply(n, arguments)
                    : n.queue.push(arguments)
            }
            if (!f._fbq) f._fbq = n
            n.push = n
            n.loaded = !0
            n.version = "2.0"
            n.queue = []
            t = b.createElement(e)
            t.async = !0
            t.src = v
            s = b.getElementsByTagName(e)[0]
            s.parentNode.insertBefore(t, s)
        })(
            window,
            document,
            "script",
            "https://connect.facebook.net/en_US/fbevents.js"
        )
        this.facebookExternalId = thinkingdata.getDeviceId()
        // 初始化
        fbq('init', this.currentPixelId, {
            external_id: this.facebookExternalId,
        })
        this.facebookResult = fbq
        this.trackEvent(this.facebookTrackMap.get('PageView'))
        log(`ChannelChange [${this.currentText()} ]: 
        historyPixelId:${this.historyPixelId}
        currentPixelId:${this.currentPixelId} 创建  调用成功`)
        console.log(`~~~ChannelChange [${this.currentText()}]: init external_id ~~~~~~`, this.facebookExternalId)
        const facebookCookies = this.getFacebookCookies()
        console.log(`~~~ChannelChange [${this.currentText()}]: 获取cookie _fbp~~~~~~`, facebookCookies.fbp)
        console.log(`~~~ChannelChange [${this.currentText()}]: 获取cookie _fbc~~~~~~`, facebookCookies.fbc)
    }
    // TikTok Pixel 初始化方法
    static tiktokPixedlIdInit() {
        // https://business-api.tiktok.com/portal/docs?id=1739585702922241#item-link-%E7%BD%91%E7%AB%99%E6%A0%87%E5%87%86%E4%BA%8B%E4%BB%B6
        !function (w, d, t) {
            w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};
        }(window, document, 'ttq')
        // TODO: 下面的代码需要用 
        // 1 window.requestIdleCallback
        // 2 如果1没有就用 setTimeout
        // let t1 = window.setTimeout // 异步，宏任务，比微任务优先级低
        // if (window.requestIdleCallback) {
        //     // 插入一个函数，这个函数将在浏览器空闲时期被调用。这使开发者能够在主事件循环上执行后台和低优先级工作，
        //     // 而不会影响延迟关键事件，如动画和输入响应。函数一般会按先进先调用的顺序执行，
        //     // 然而，如果回调函数指定了执行超时时间timeout，则有可能为了在超时前执行函数而打乱执行顺序。
        //     t1 = window.requestIdleCallback
        // }
        // t1(() => {
        //     log(`ChannelChange [tiktok]: 
        //     【tiktok埋点】发送`)
        // })
        ttq.load(this.currentPixelId)
        ttq.page() // 将报告一个页面视图事件，可用于验证Pixel代码安装情况。
        this.tiktokResult = ttq
        this.trackEvent(this.tiktokTrackMap.get('ViewContent'),{
            content_id: getUrlParams('book_id'),// 书籍id
        })
        log(`ChannelChange [${this.currentText()}]: 
        historyPixelId:${this.historyPixelId} 
        currentPixelId:${this.currentPixelId} 创建  调用成功`)
    }
    // Snapchat Pixel 初始化方法
    static snapchatPixedlIdInit() {
        /**
         * 特殊处理
         * 特殊处理
         * 特殊处理
         * 问题：
         *      因为 snapchat 的 init 方法无法多次初始化，所以需要特殊处理
         * 解决方法：
         *      1. 第一个传递的 pixeldId 跳过，因为是从URL上获取的，当是第二个传递的 pixeldId 时，才进行初始化
         */
        let historyPixelIdLength = this.historyPixelId.length
        if (historyPixelIdLength === 1) {
            console.log(`🤡  调试 snapchatPixedlIdInit 跳过初始化 (第一个 pixeldId（url上获取的）: ${this.currentPixelId} 跳过), 历史pixelId: `, this.historyPixelId)
            return
        }
        if (historyPixelIdLength === 2) {
            console.log(`🎉  调试 snapchatPixedlIdInit 开始初始化 (第二个 pixelId（api获取的）: ${this.currentPixelId} 可以), 历史pixelId: `, this.historyPixelId)
        }

        (function(e, t, n) {
            if (e.snaptr) return;
            var a = e.snaptr = function() {
                a.handleRequest ? a.handleRequest.apply(a, arguments) : a.queue.push(arguments);
            };
            a.queue = [];
            var s = 'script';
            var r = t.createElement(s);
            r.async = !0;
            r.src = n;
            var u = t.getElementsByTagName(s)[0];
            u.parentNode.insertBefore(r, u);
        })(window, document, 'https://sc-static.net/scevent.min.js');
        this.snapchatExternalId = thinkingdata.getDeviceId()
        console.log("🚀 ~ ChannelChange ~ snapchatPixedlIdInit ~ thinkingdata.getDeviceId():", thinkingdata.getDeviceId())

        // 初始化 https://businesshelp.snapchat.com/s/article/pixel-direct-implementation?language=en_US
        snaptr('init', this.currentPixelId, {
            external_id: this.snapchatExternalId, // TODO: 这里不知道snapchat是不是这样子操作的，需要去看一下文档
        })
        
        this.snapchatResult = snaptr
        console.log('调试 snapchatPixedlIdInit 当前初始化 pixelId', this.currentPixelId)
        this.trackEvent(this.snapchatTrackMap.get('PAGE_VIEW'));
        log(`ChannelChange [${this.currentText()} ]: 
        historyPixelId:${this.historyPixelId}
        currentPixelId:${this.currentPixelId} 创建  调用成功`)
        console.log(`~~~ChannelChange [${this.currentText()}]: init external_id ~~~~~~`, this.snapchatExternalId)
        const snapchatCookies = this.getSnapchatCookies()
        console.log(`~~~ChannelChange [${this.currentText()}]: 获取cookie _scid~~~~~~`, snapchatCookies.scid)
    }
    // Pinterest Pixel 初始化方法
    static pinterestPixedlIdInit() {
        // Pinterest 标签初始化
        // https://help.pinterest.com/zh-hans/business/article/add-event-codes
        !function(e){if(!window.pintrk){window.pintrk = function () {
            window.pintrk.queue.push(Array.prototype.slice.call(arguments))
        };var n=window.pintrk;n.queue=[],n.version="3.0";var t=document.createElement("script");
        t.async=!0,t.src=e;var r=document.getElementsByTagName("script")[0];
        r.parentNode.insertBefore(t,r)}}("https://s.pinimg.com/ct/core.js");

        this.pinterestExternalId = thinkingdata.getDeviceId()
        console.log("🚀 ~ ChannelChange ~ pinterestPixedlIdInit ~ thinkingdata.getDeviceId():", thinkingdata.getDeviceId())

        /* 
            //https://help.pinterest.com/zh-hans/business/article/install-the-base-code
            pintrk('setconsent', true) 
        */
        // 初始化 Pinterest 标签
        pintrk('load', this.currentPixelId, {
            external_id: this.pinterestExternalId, // 使用外部ID作为邮箱标识符
        })
        pintrk('page')

        this.pinterestResult = pintrk
        console.log('调试 pinterestPixedlIdInit 当前初始化 pixelId', this.currentPixelId)
        this.trackEvent(this.pinterestTrackMap.get('ViewContent'), {
            event_id: this.currentPixelId,
        });
        log(`ChannelChange [${this.currentText()} ]:
        historyPixelId:${this.historyPixelId}
        currentPixelId:${this.currentPixelId} 创建  调用成功`)
        console.log(`~~~ChannelChange [${this.currentText()}]: init external_id ~~~~~~`, this.pinterestExternalId)
        console.log(`~~~ChannelChange [${this.currentText()}]: 获取cookie _epik~~~~~~`, this.getPinterestClickId())
    }
    // 是否是公共买量流量
    isPublicBuyTraffic() {

    }
    // 点击按钮 - 跳转到商城
    static buttonChangleTrack() {
        if (this.isTiktok) {
            this.trackEvent(this.tiktokTrackMap.get('ClickButton'))
        } else if (this.isSnapchat) {
            this.trackEvent(this.snapchatTrackMap.get('AD_CLICK'))
        } else if (this.isPinterest) {
            this.trackEvent(this.pinterestTrackMap.get('ClickButton'))
        }
    }
    // 滚动到底部
    static viewContentFinishChangleTrack() {
        if (this.isTiktok) {
            this.trackEvent(this.tiktokTrackMap.get('ViewContentFinish'), {})
        }
        else if (this.isSnapchat) {
            this.trackEvent(this.snapchatTrackMap.get('AD_VIEW'))
        }
        else if (this.isPinterest) {
            this.trackEvent(this.pinterestTrackMap.get('ViewContentFinish'))
        }
        // 产品说，这个事件没有对应的，所以展示不加
        // else if (this.isFacebook) {
        // this.trackEvent(this.facebookTrackMap.get('PageContentFinish'), {})
        // }
    }
    // 获取 ExternalThinkDataId
    static getExternalThinkDataId() {
        let id = null
        if (this.isFacebook) {
            id = this.facebookExternalId
        } else if (this.isSnapchat) {
            id = this.snapchatExternalId
        } else if (this.isPinterest) {
            id = this.pinterestExternalId
        }
        // 其他渠道没有 ExternalThinkDataId

        // console.error(`ChannelChange: getExternalId:${id}`)
        return id
    }
    static publicPixedlIdInit(pixelId) {
        let id = pixelId
        this.currentPixelId = id
        // TIP: 这里 facebook是字符串内是数字，但是 tiktok 是英文加数字，那就直接不转为数字了
        // this.historyPixelId.push(Number(this.currentPixelId))
        this.historyPixelId.push(this.currentPixelId)
        let id1 = this.historyPixelId[0]
        let id2 = this.historyPixelId[1]

        // 特殊处理 snapchat 的初始化
        if (this.isSnapchat) {
            this.snapchatPixedlIdInit();
        }

        if (id1 === id2) {
            log(`ChannelChange: historyPixelId:${this.historyPixelId}`)
            log(`ChannelChange: id1:${id1} id2:${id2} 相同 不重新初始化`)
            return
        }

        if (this.isFacebook) {
            this.facebookPixedlIdInit()
        } else if (this.isTiktok) {
            this.tiktokPixedlIdInit()
        } else if (this.isPinterest) {
            this.pinterestPixedlIdInit()
        }
    }
    // log 所有渠道点击 id 并返回
    static logAllChannelClickId() {
        // 渠道点击ide
        let channelClickIds = {
            [channelHelp.FACEBOOK_URL_PARAMS_ENUM.channelClickId.key]: {
                name: ChannelChange.channelMaps.facebook.text,
                value: null,
            },
            [channelHelp.SNAPCHAT_URL_PARAMS_ENUM.channelClickId.key]: {
                name: ChannelChange.channelMaps.snapchat.text,
                value: null,
            },
            [channelHelp.PINTEREST_URL_PARAMS_ENUM.channelClickId.key]: {
                name: ChannelChange.channelMaps.pinterest.text,
                value: null,
            },
        }
        // 循环赋值
        for (const key in channelClickIds) {
            let urlKey = key
            let urlValue  = getUrlParams(key)
            if (urlKey && urlValue) {
                channelClickIds[key].value = urlValue
            }
        }
        console.log("🚀 ~ channelClickIds:",channelClickIds, JSON.stringify(channelClickIds, null, 4))

        return channelClickIds
    }
    // 设置渠道
    static setSource(channel) {
        let s = 'web2apps'
        let c = channel && channel.toLowerCase()
        if (c === 'facebook_ip_int') {
            s = getUrlParams('source'),// 渠道名，之前是写死, 现在直接拿url上的就可以 'web2app_fb' 
            this.isFacebook = true
            this.currentChannelKey = this.channelMaps.facebook.key
        } else if (c === 'tiktok_ip_int') {
            s = getUrlParams('source'),// 渠道名，之前是写死, 现在直接拿url上的就可以 'web2app_tt' 
            this.isTiktok = true
            this.currentChannelKey = this.channelMaps.tiktok.key
        } else if (c === 'snapchat_ip_int') {
            s = getUrlParams('source')
            this.isSnapchat = true
            this.currentChannelKey = this.channelMaps.snapchat.key
        } else if (c === 'google_ip_int') {
            s = getUrlParams('source'),// 渠道名，之前是写死, 现在直接拿url上的就可以 'web2app_google'
            this.isGoogle = true
            this.currentChannelKey = this.channelMaps.google.key
        } else if (c === 'pinterest_ip_int') {
            s = getUrlParams('source'),// 渠道名，之前是写死, 现在直接拿url上的就可以 'web2app_pt'
            this.isPinterest = true
            this.currentChannelKey = this.channelMaps.pinterest.key
        }
        else {
            console.error(`ChannelChange: setSource:${channel} error`)
            alert(`setSource 未识别到渠道 channel:${channel}`)
        }
        this.currentSource = s
        log(`ChannelChange: channel:${channel} currentSource:${this.currentSource} 初始化`)
    }
    static getSource() {
        return this.currentSource
    }
    static getPixedId() {
        return this.currentPixelId
    }
}
window.ChannelChange = ChannelChange