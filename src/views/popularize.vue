<template>
    <!-- 
     https://dev-h5-personal.fantastory.cc/h5popularize_wangwen_public/popularize?debug=1&closeAppstoreLocation=1&stop=true&testSecretKey=kvBvawSSWrntdmZp&packageName=com.magfic.novel.buu&channel=facebook_ip_int&source=web2app_fb&cpm=jtB3-vtxhqUG9ZJeoavW9w&book_id=27&book_version=0&book_version_name=origin&chapter_id=2&pixelId=200075699619404&utm_campaign=%7B%7Bcampaign.name%7D%7D&utm_campaignid=%7B%7Bcampaign.id%7D%7D&utm_ad=%7B%7Bad.name%7D%7D&utm_adset=%7B%7Badset.name%7D%7D&af_r=http%3A%2F%2Fwww.google.com
     
     -->
    <div>
        <!-- 动态: 整个背景颜色 -->
        <div
            class="pople"
            :style="[lineStyleFilter(bookData.backgroundCss)]"
            v-if="loaded"
        >
            <!-- 置顶下载 注意是有开关的 -->
            <template v-if="downloadAboutSwitchCpt">
                <!-- 置顶下载 --- 纯图片 -->
                <div
                    v-if="downloadAboutImageTypeCpt"
                    class="pople__downloadfixed"
                    @click="schemeUpApp"
                >
                    <img
                        class="pople__downloadfixed__img"
                        style="width: 100%;"
                        :src="downloadAboutCpt.topDownloadPicUrl"
                    />
                </div>

                <!-- 置顶下载 --- html -->
                <div class="pople__downloadfixed"
                    v-if="downloadAboutHtmlTypeCpt"
                    :style="[
                        lineStyleFilter(
                            downloadAboutCpt.topDownloadBackgroundCss
                        )
                    ]"
                >
                    <!-- 左 1 项目LOGO  -->
                    <div class="downloadfixed__left__container">
                        <!-- 左 1 项目LOGO  -->
                        <div class="downloadfixed__left__logoimage">
                            <img :src="getCurrentLogoImage" alt=""/>
                        </div>
                        <!-- 左 2 标题和内容  -->
                        <div class="downloadfixed__left__texts">
                            <div class="downloadfixed__left__title"
                                :style="[
                                    lineStyleFilter(
                                        downloadAboutCpt.topDownloadTitleCss
                                    )
                                ]"
                            >
                                 {{ downloadAboutCpt.topDownloadTitle }}
                            </div>
                            <div class="downloadfixed__left__content"
                                :style="[
                                    lineStyleFilter(
                                        downloadAboutCpt.topDownloadContentCss
                                    )
                                ]"
                            >
                                {{ downloadAboutCpt.topDownloadContent }}
                            </div>
                        </div>
                    </div>
                    <!-- 右 下载按钮 -->
                    <div class="downloadfixed__right__container" @click="schemeUpApp">
                        <div class="downloadfixed__right__button"
                            :style="[
                                lineStyleFilter(
                                    downloadAboutCpt.topDownloadButtonCss
                                )
                            ]"
                        >
                            <span class="downloadfixed__right__text">
                                {{ 
                                    downloadAboutCpt.topDownloadButtonText || 'Download'
                                }}
                            </span>
                        </div>
                    </div>
                </div>
                <!-- 高度页面 -->
                <div class="pople__downloadfixed__defaultheight"></div>
            </template>

            <!-- 主题2 -->
            <template v-if="themeType2Cpt">
                <img
                    class="pople__banner__container pople__banner__container-them2"
                    :src="bookData.bannerUrl"
                    v-if="
                        bookData.cover && ![2, 3].includes(bookData.style)
                    "
                />
            </template>

            <!-- 主题1 -->
            <div class="pople__topbanner" v-if="themeType1Cpt">
                <!-- 头部 -->
                <div class="pople__header">
                    <!-- 背景 -->
                    <div class="header__background">
                        <!-- src="../img/img_public/ezgif.com-gif-maker.webp"  -->
                        <!-- :style="{
                            backgroundImage: `url(${bookData.bannerUrl})`
                        }"  -->
                        <img
                            :src="bookData.bannerUrl"
                            v-if="
                                bookData.cover && ![2, 3].includes(bookData.style)
                            "
                        />
                    </div>
                    <div class="header__config" @click="schemeUpApp">
                        <!-- 左侧 小说图片-->
                        <div
                            class="header__img"
                        >
                            <!-- :src="bookData.cover" -->
                            <img
                                :style="{
                                    backgroundImage: `url(${bookData.cover})`
                                }"
                                alt=""
                            />
                        </div>
                        <!-- 右侧 小说标题 -->
                        <div class="header__text">
                            {{ bookData.bookName }}
                        </div>
                    </div>
                    <!-- 头部遮罩层 - 渐变效果 -->
                    <div class="header__mask" :style="{
                            background: `linear-gradient(
                                180deg,
                                rgba(255, 251, 243, 0) 0%,
                                rgba(255, 251, 243, 0.6) 39%,
                                ${
                                    bookData.backgroundCss && bookData.backgroundCss.backgroundColor
                                    ? bookData.backgroundCss.backgroundColor
                                    : '#fbf8f2'
                                }
                                100%
                            )`
                        }">
                        <!-- 遮罩渐变 -->
                    </div>
                </div>
            </div>

            <!-- 书本名 主题2 主题3 都显示，但是不同的样式 -->
            <div class="pople__bookname"
                :class="{
                    'pople__bookname--them2':
                    themeType2Cpt,
                    'pople__bookname--them3': 
                    themeType3Cpt,
                }"
                v-if="themeType3Cpt || themeType2Cpt"
            >
                {{ bookData.bookName }}
            </div>

            <!-- 内容引言 - 样式1 -->
            <div v-if="judgmentIntroductionType(PopularizeFetchEnum.introStyleEnum.引言类型1)" class="pople__default__introduction pople__introduction"
                :style="[
                    cptGetIntroductionCss
                ]"
            >
                 <!-- 引号 -->
                 <div class="pople__introduction__leftmodel" 
                    :style="[
                        lineStyleFilter(
                            introAboutCpt.introTextCss.color
                        )
                    ]"
                 >
                    “
                 </div>
                 <span>&nbsp;</span>
                <!-- 文字内容 -->
                 <div class="pople__introduction__contentmodel"
                 >
                    <!-- 可以显示换行，但是这里的布局会有问题，待修复 -->
                    <!-- <div v-html="introAboutCpt.introText" style="white-space: pre-wrap;"></div> -->
                    {{ introAboutCpt.introText }}
                    <span>&emsp;</span>
                 </div>
                 <!-- 引号 -->
                 <div class="pople__introduction__rightmodel"
                    :style="[
                        lineStyleFilter(
                            introAboutCpt.introTextCss.color
                        )
                    ]"
                 >
                    ”
                 </div>
            </div>
            <!-- 内容引言 - 样式2 -->
            <div v-if="judgmentIntroductionType(PopularizeFetchEnum.introStyleEnum.引言类型2)" class="pople__default__introduction pople__2introduction"
                :style="[
                    cptGetIntroductionCss
                ]"
            >
                <!-- 修饰图 -->
                <div class="pople__publicimage__introduction--0 pople__publicimage__introduction--1">
                </div>
                <!-- 文字内容 -->
                <div class="pople__2introduction__contentmodel"
                >
                    {{ introAboutCpt.introText }}
                </div>
                <!-- 修饰图 -->
                <div class="pople__publicimage__introduction--0 pople__publicimage__introduction--4">
                </div>
            </div>
            <!-- 内容引言 - 样式3 -->
            <div v-if="judgmentIntroductionType(PopularizeFetchEnum.introStyleEnum.引言类型3)" class="pople__default__introduction pople__3introduction"
                :style="[
                    cptGetIntroductionCss
                ]"
            >
                <!-- 修饰图 -->
                <div class="pople__publicimage__introduction--0 pople__publicimage__introduction--1">
                </div>
                <!-- 修饰图 -->
                <div class="pople__publicimage__introduction--0 pople__publicimage__introduction--2">
                </div>
                <!-- 文字内容 -->
                <div class="pople__3introduction__contentmodel">
                    {{ introAboutCpt.introText }}
                </div>
                <!-- 修饰图 -->
                <div class="pople__publicimage__introduction--0 pople__publicimage__introduction--3">
                </div>
                <!-- 修饰图 -->
                <div class="pople__publicimage__introduction--0 pople__publicimage__introduction--4">
                </div>
            </div>

            <!-- 主题3 -->
            <template v-if="themeType3Cpt">
                <img class="pople__banner__container pople__banner__container-them3"
                    :src="bookData.bannerUrl"
                    v-if="
                        bookData.cover && ![2, 3].includes(bookData.style)
                    "
                />
            </template>

            <!-- 章节内容 -->
            <div class="pople__content">
                <!-- 富文本内容1 -->
                <div
                    class="content__ritch content__ritchtext1 text__autowrap"
                    v-html="bookData.firstIeditText"
                ></div>

                <!-- 小说章节内容 -->
                <div class="content__body">
                    <div class="content__canvas" id="content__canvas">
                        <div
                            class="content__book-content"
                            id="content__book-content"
                        >
                            <!-- 遍历章节列表 -->
                            <div
                                v-for="item in bookData.popularizeChapterDetails"
                            >
                                <!-- 章节名 -->
                                <div class="content__chapterName"
                                    :style="[
                                        chapterNameStyle()
                                    ]"
                                >
                                    {{ item.chapterName }}
                                </div>
                                <!-- 章节详细内容 -->
                                <div class="content__noveldes">
                                    <div
                                        v-for="(
                                            chapter, index
                                        ) in item.chapterContent"
                                        :key="index"
                                    >
                                        <!-- 章节段落内容 -->
                                        <div
                                            class="content__content-item"
                                            :style="[
                                                lineStyleFilter(
                                                    bookData.chapterContentCss
                                                )
                                            ]"
                                        >
                                            {{ chapter }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 富文本内容2 -->
                <div
                    class="content__ritch content__ritchtext2 text__autowrap"
                    v-html="bookData.secondIeditText"
                ></div>

                <!-- 文字引导 -->
                <div
                    class="pople__html"
                    v-if="bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.文字"
                    v-html="bookData.transGuideText" 
                    :style="lineStyleFilter(bookData.transGuideTextCss)"
                >
                </div>
                <!-- 图片引导 -->
                <div
                    class="pople__img"
                    v-if="bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.图片"
                >
                    <img v-if="bookData.transGuidePicUrl":src="bookData.transGuidePicUrl" />
                    <img v-else src="@/img/img_public/按钮引导动图.webp" alt="" srcset="">
                </div>


                <!-- 占据高度（这里的高度，相当于滚动到底部弹出的图片高度） -->
                <!-- <div class="sizebox"></div> -->
            </div>

            <!-- 底部按钮区域 -->
            <div class="pople__download__box"
                :style="[lineStyleFilter(bookData.backgroundCss)]"
            >
                <!-- 滚动到底部才展示的气泡提示 -->
                <!-- v-show="showBubble" -->
                    
                <!-- <template v-if="bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.图片">
                    <div class="pople__bubble pople__bubble--img" 
                        :style="{ visibility: showBubble && bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.图片 ? 'visible' : 'hidden' }"
                    >
                        <img v-if="bookData.transGuidePicUrl"
                            :src="bookData.transGuidePicUrl"
                        />
                        <img v-else src="@/img/img_public/按钮引导动图.webp" alt="" srcset="">
                    </div>
                 </template> -->

                <!-- 跳转市场按钮区域 -->
                <div class="pople__download__container">
                    <!-- 下载按钮 -->
                    <div
                        class="pople__download__button"
                        :class="{
                            // 默认按钮样式
                            'pople__download__button--default':
                                cptTransButtonCssTypeNotSet ||
                                cptTransButtonCssTypeHtml,
                            // 图片按钮样式    
                            'pople__download__button--img':
                                cptTransButtonCssTypeImage,

                            // 按钮动画效果
                            'pople__download__button--animiatoin__emphasize': // 发光效果 ｜ 呼吸灯
                            cptTransButton3TypeCpt,
                            'pople__download__button--animiatoin__pulse': // 放大缩小
                            cptTransButton2TypeCpt,
                        }"
                        :style="[
                            cptTransButtonCssTypeHtml ? lineStyleFilter(bookData.transButtonCss) : '',
                            {
                                // 发光效果需要用到的阴影颜色
                                // '--emphasize-box-shadow-color': bookData.transButtonCss._emphasizeBoxShadowColor
                                '--emphasize-box-shadow-color': cptTransButtonAnimationType.glowingShadowColor
                            },
                        ]"
                        @click="schemeUpApp"
                    >
                        <!-- 图片按钮 -->
                        <img
                            v-if="cptTransButtonCssTypeImage"
                            :src="bookData.transButtonPicUrl"
                        />

                        <!-- 文字按钮 -->
                        <div
                            v-if="cptTransButtonCssTypeNotSet || cptTransButtonCssTypeHtml"
                            class="pople__download__html">
                            <template v-if="bookData.buttonText">
                                {{ bookData.buttonText }}
                            </template>
                            <template v-else>
                                Download to Read More >>
                            </template>

                            <!-- 扫光效果 ｜ 流光效果 -->
                            <template v-if="cptTransButton4TypeCpt">
                                <img src="../img/img_public/<EMAIL>" alt="" class="skeleton__animate__img skeleton__animate__img--1">
                            </template>
                        </div>
                        
                        <!-- 手指动画 -->
                        <FingersLottie
                            v-if="cptTransButton1TypeCpt"
                            class="pople__download__webp"
                        ></FingersLottie>
                    </div>

                    <!-- 底部提示文案 -->
                    <div
                        class="pople__download__tip"
                        :style="[lineStyleFilter(bookData.transButtonTipCss)]"
                    >
                        {{
                            bookData.transButtonTipText ||
                            "Read the full story after installation"
                        }}
                    </div>
                </div>
            </div>
        </div>
        <!-- 加载中状态 -->
        <div class="pages--loading" v-else>
            <van-loading size="100" />
        </div>
    </div>
</template>

<script>
    import lodash from 'lodash'
    // 引入相关依赖
    import mockData, { lineStyleFilter, PopularizeFetchEnum } from "./mockData"
    import "@/fonts/inter/inter-font.css"
    import "@/fonts/other/other-font.css"
    import { mapState } from "vuex"
    import VueClipBoard from "vue-clipboard2"
    import uaOs from '@/utils/uaOs.js';
    import store from "@/store"
    import axios from "axios"
    import Vue from "vue"
    import {ChannelChange,getUrlParams} from './ChannelChange'
    import * as nameConfig from '../../sassConfig/nameConfig'
    import FingersLottie from '../components/FingersLottie.vue'
    import * as channelHelp from './channelHelp'

    Vue.use(VueClipBoard)

    const systemTypeGet = () => {
        const ua = navigator.userAgent || '';
        const isAndroid = /android/i.test(ua);
        const isIos = /iphone|ipod/i.test(ua);
        return {
            isAndroid,
            isIos,
        }
    }
    
    // 判空工具函数
    const checkNotValue = (value) => {
        let vl = value
        let a1 = vl === null
        let a2 = vl === undefined
        let a3 = (vl && vl.trim()) === ''
        return a1 || a2 || a3
    }

    // 睡眠 promise 函数
    const sleep = (time) => {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve()
            }, time)
        })
    }

    // 用于调试的mock数据
    const mockObject = {
        bookData: mockData,
        showBubble: false,
        clipboardTxt: 1,
        loaded: true,
    }

    export default {
        name: "popularize",

        components: {
            FingersLottie,
        },

        computed: {
            // debug 永远展示所有类型
            foreverShowEveryTypeCheckCpt() {
                return this.bookData._debugShowAllType
            },

            // 按钮动画类型
            cptTransButtonAnimationType() {
                let s = {}
                let e = this.bookData.transButtonAnimationParam
                if (lodash.isEmpty(e)) {
                    console.error('按钮动画类型配置错误')
                } else {
                    s = e
                }
                return s
            },
            // 内容引言样式配置
            introAboutCpt() {
                let s = {}
                let e = this.bookData.introAbout
                if (lodash.isEmpty(e)) {
                    console.error('内容引言样式配置错误')
                } else {
                    s = e
                }
                return s
            },
            // 下载按钮区域样式配置
            downloadAboutCpt() {
                // topUp
                let s = {}
                let e = this.bookData.topDownloadAbout
                if (lodash.isEmpty(e)) {
                    console.error('下载按钮区域样式配置错误')
                } else {
                    s = e
                }
                return s
            },
            // 下载按钮区域是否展示开关
            downloadAboutSwitchCpt() {
                return this.downloadAboutCpt.isShowTopDownload === PopularizeFetchEnum.switchEnum.开启
            },
            // 下载类型为图片
            downloadAboutImageTypeCpt() {
                return this.downloadAboutCpt.topDownloadCssType === PopularizeFetchEnum.innterhtmlTypeNum.图片
            },
            // 下载类型为html
            downloadAboutHtmlTypeCpt() {
                return this.downloadAboutCpt.topDownloadCssType === PopularizeFetchEnum.innterhtmlTypeNum.文字
            },

            themeType1Cpt() {
                return this.checkThemeType(1)
            },
            // 主题2
            themeType2Cpt() {
                return this.checkThemeType(2)
            },
            // 主题3
            themeType3Cpt() {
                return this.checkThemeType(3)
            },

            // 按钮类型
            cptTransButton1TypeCpt() {
                return this.checkTransButtonType(PopularizeFetchEnum.transButtonTypeEnum.手指1)
            },
            // 按钮类型2
            cptTransButton2TypeCpt() {
                return this.checkTransButtonType(PopularizeFetchEnum.transButtonTypeEnum.缩放2)
            },
            // 按钮类型3
            cptTransButton3TypeCpt() {
                return this.checkTransButtonType(PopularizeFetchEnum.transButtonTypeEnum.发光3)
            },
            // 按钮类型4
            cptTransButton4TypeCpt() {
                return this.checkTransButtonType(PopularizeFetchEnum.transButtonTypeEnum.流动4)
            },

            // 数数设备信息
            ...mapState("user", ["shushuDeviceMsg"]),
            
            // 按钮类型计算属性
            // 未设置按钮类型
            cptTransButtonCssTypeNotSet() {
                // 情况1 没有设置类型
                // 情况2 设置的是图片模式 但是没有传图片的情况
                return !this.bookData.transButtonCssType || (this.bookData.transButtonCssType === "PICTURE" && !this.bookData.transButtonPicUrl)
            },
            // HTML按钮类型
            cptTransButtonCssTypeHtml() {
                return this.bookData.transButtonCssType === "HTML"
            },
            // 图片按钮类型
            cptTransButtonCssTypeImage() {
                return this.bookData.transButtonCssType === "PICTURE" && this.bookData.transButtonPicUrl
            },
            getCurrentLogoImage() {
                let imgResult = null
                try {
                    let name = GloablEnvConfig.currentModleName
                    imgResult = require(`../img/img_${name}/${name}__logo.png`)
                    // imgResult = require('../img/img_public/logo/'+ nameConfig.currentModleName +'.png')
                } catch (error) {
                    console.error('加载项目logo失败:', error)
                    imgResult = ''
                }
                return imgResult
            },
            // 内容引言 - 样式
            cptGetIntroductionCss() {
                let s = {
                }
                let v1 = this.introAboutCpt
                if (v1) {
                    s = lineStyleFilter(
                        v1.introTextCss
                    )
                }
                return s
            },
        },

        data() {
            return {
                // 小说数据
                bookData: {},
                // 到达底部的图片显示标志
                showBubble: false,
                // 归因链接
                oneLinkURLGO: "",
                // 网页待机时间
                browseTime: 0, // 浏览时长初始值为 0
                // 定时器
                timeInterval: null,
                // 复制的文本
                clipboardTxt: "",
                // 是否目前是初始化中
                initPage: null,
                // 不知道什么东西
                dataAfR: "",
                // 点击按钮的类型  button(正常点击) | auto(facebook点击)
                clickButtonType: null,
                // 是否从facebook广告数据页面点进来的
                adsModel: false,
                adsPageDataLoaded: [],
                // 数据是否加载完成
                loaded: false,
                getCookieInterfavl: null,
                loopTime: null,
                loopData: [],
                loopSize: 0,
                PopularizeFetchEnum,
            }
        },

        watch: {
            // facebook 数据加载统计监听
            adsPageDataLoaded(newValue, oldValue) {
                // 加载获取书本ajax数据后需要再处理2个事件，所以要等2个事件处理完成之后，再触发自动点击操作
                let isAuto = newValue.length === 2 && this.adsModel
                if (isAuto) {
                    this.autoBtn()
                }
            },
            // 书本数据加载完成
            loaded(newValue, oldValue) {
                const height = this.getPopleBubbleHeight()
                console.log("🚀 ~ loaded ~ height:", height)
                console.log("🚀 ~ loaded ~ newValue, oldValue:", newValue, oldValue)
            }
        },

        methods: {
            // 样式过滤器
            lineStyleFilter,

            // 唤起app
            async schemeUpApp() {
                const goToApp = () => {
                    if (this.$route.query.closeAppstoreLocation) {
                        return
                    }
                    // 清理iframe后再调用setupApp
                    this.cleanupSchemeIframe()
                    this.storeBtn()
                }
                
                const { isAndroid, isIos } = systemTypeGet()
                
                if (isAndroid) {
                    this.cleanupSchemeIframe()
                    
                    // 检查Schema URL
                    let schemeUrl = window.GloablEnvConfig.ANDROID_SCHEMA
                    if (!schemeUrl) {
                        console.error('唤起app的schema未配置')
                        goToApp()  // 配置错误时直接跳转应用商店
                        return
                    }
                    
                    // 创建新iframe
                    const iframe = document.createElement('iframe')
                    iframe.id = 'app-scheme-iframe'
                    iframe.style.display = 'none'
                    iframe.src = schemeUrl
                    
                    document.body.appendChild(iframe)
                    
                    try {
                        // 等待一定时间检查是否唤起成功
                        await sleep(300)

                        // 检查iframe是否仍然存在，存在则表示唤起失败
                        if (document.getElementById('app-scheme-iframe')) {
                            goToApp()  // 跳转到应用商店
                        }
                    } finally {
                        // 无论成功失败都清理iframe
                        this.cleanupSchemeIframe()
                    }
                    
                    return
                }
                
                // iOS直接跳转
                goToApp()
            },

            // 清理schema iframe的辅助方法
            cleanupSchemeIframe() {
                const oldIframe = document.getElementById('app-scheme-iframe')
                if (oldIframe && oldIframe.parentNode) {
                    oldIframe.parentNode.removeChild(oldIframe)
                }
            },

            // 检查主题类型的通用方法
            checkThemeType(type) {
                return +this.bookData.themeType === type || this.foreverShowEveryTypeCheckCpt
            },
            
            // 检查按钮类型的通用方法
            checkTransButtonType(type) {
                return +this.cptTransButtonAnimationType.buttonAnimationType === type || this.foreverShowEveryTypeCheckCpt
            },
            
            // 内容引言 类型判断
            judgmentIntroductionType(type) {
                // 获取内容引言配置对象
                let v1 = this.introAboutCpt
                // 获取配置中的引言类型
                let a = v1 && v1.introStyle
                // 当前传入的类型
                let b = type
                // 获取配置中的引言是否展示开关
                let c = v1.isShowIntro === PopularizeFetchEnum.switchEnum.开启
                // 判断是否匹配当前类型，或者是否设置了永久显示所有类型的标志
                if (c) {
                    return +a === +b || this.foreverShowEveryTypeCheckCpt
                }
                return false
            },
            
            // 章节名样式
            chapterNameStyle() {
                let cStyle = {}
                let css = this.bookData.chapterContentCss
                if (css && css.color) {
                    cStyle.color = css.color
                }
                return cStyle
            },
            
            // 获取路由参数
            getRouteQuery() {
                return structuredClone(this.$route.query)
            },
            
            // 复制文本
            async doCopy() {
                let { text } = await this.$copyText(this.clipboardTxt)
                console.log("复制归因链接:", text, JSON.parse(text))
            },
            
            // 页面销毁前生命周期
            beforeunloadHandler(e) {
                clearInterval(this.timeInterval) // 离开页面后清除定时器
                this.timeInterval = null
                this.数数打点("view_time") // 上送后台接口，将浏览时长等信息传到后台，离开当前路由后调用
                this.initPage = null
            },
            
            // 唤起app
            async setupApp() {
                ChannelChange.buttonChangleTrack()
                this.数数打点("btn_click")
                this.doCopy()

                const url = this.oneLinkURLGO
                console.log("🚀 ~ setupApp ~ url:", url)
                if (this.$route.query.closeAppstoreLocation) {
                    return
                }
                await sleep(500)
                window.location.href = url
            },

            // 按钮唤起app
            storeBtn() {
                this.clickButtonType = "button"
                this.setupApp()
            },

            // 导航图唤起app
            bannerBtn() {
                this.clickButtonType = "banner"
                this.setupApp()
            },

            // 自动点击按钮，来源是: facebook 广告点击
            autoBtn() {
                this.clickButtonType = "auto"
                this.setupApp()
            },

            // tiktok归因
            归因tiktok() {
                return this.归因public()
            },
            
            // facebook归因
            归因facebook() {
                return this.归因public()
            },

            // snapchat归因
            归因snapchat() {
                return this.归因public()
            },

            // pinterest归因
            归因pinterest() {
                return this.归因public()
            },
            
            // 目前是 facebook 和 tiktok 同一个脚本文件，传递的参数也是一样
            归因public() {
                /**
                 * TODO: keys: [facebook参数, tiktok参数,]
                 */
                //正在初始化智能脚本参数
                //Initializing Smart Script arguments
                let mediaSource = {
                    keys: [
                        // "utm_source", // 废除，统一用下面的参数获取就可以
                        "source"
                    ],
                    // defaultValue: "Web2Apps", // 兼容旧的，所以这里还是之前的值
                    defaultValue: "web2app_default", // 兼容旧的，所以这里还是之前的值
                }
                let campaign = {
                    keys: [
                        // "utm_campaign",
                        "af_c", 
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignName.key,
                    ],
                }
                let afSub1 = {
                    keys: [
                        // "fbclid",
                        channelHelp.FACEBOOK_URL_PARAMS_ENUM.channelClickId.key,
                        channelHelp.SNAPCHAT_URL_PARAMS_ENUM.channelClickId.key,
                        channelHelp.PINTEREST_URL_PARAMS_ENUM.channelClickId.key,
                        "ttclid",
                    ],
                }
                let googleClickIdKey = "af_sub2"
                let deepLinkValue = {
                    keys: ["jump_path","jump_path","jump_path",],
                    defaultValue: "reading_page",
                }
                let deep_link_sub1 = {
                    paramKey: "deep_link_sub1",
                    keys: ["book_id","book_id","book_id",],
                    defaultValue: "0",
                }
                let ad = {
                    keys: [
                        // "utm_ad",
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.adVertisementName.key,
                        "af_ad", 
                    ],
                }
                let adSet = {
                    keys: [
                        // "utm_adset",
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.adSetName.key,
                        "af_adset", 
                    ],
                }
                let af_campaignid = {
                    paramKey: "af_campaignid",
                    keys: [
                        // "utm_campaignid",
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignId.key,
                        "af_c_id", 
                    ],
                }
                let deep_link_sub2 = {
                    paramKey: "deep_link_sub2",
                    keys: ["chapter_id","chapter_id","chapter_id",],
                    defaultValue: "0",
                }
                let deep_link_sub3 = {
                    paramKey: "deep_link_sub3",
                    keys: ["source","source","source",],
                }
                let deep_link_sub4 = {
                    paramKey: "deep_link_sub4",
                    keys: [
                        // "utm_campaign",
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignName.key,
                        "af_c", 
                    ],
                }
                let deep_link_sub5 = {
                    paramKey: "deep_link_sub5",
                    keys: [
                        // "utm_campaignid",
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignId.key,
                        "af_c_id", 
                    ],
                }
                let deep_link_sub6 = {
                    paramKey: "deep_link_sub6",
                    keys: [
                        // "utm_ad",
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.adVertisementName.key,
                        "af_ad", 
                    ],
                }
                let deep_link_sub7 = {
                    paramKey: "deep_link_sub7",
                    keys: [
                        // "utm_adset",
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.adSetName.key,
                        "af_adset", 
                    ],
                }
                let deep_link_sub8 = {
                    paramKey: "deep_link_sub8",
                    keys: ["book_version","book_version","book_version",],
                    defaultValue: "0",
                }
                let deep_link_sub9 = {
                    paramKey:"deep_link_sub9",
                    keys:[
                        // "fbclid",
                        channelHelp.FACEBOOK_URL_PARAMS_ENUM.channelClickId.key,
                        channelHelp.SNAPCHAT_URL_PARAMS_ENUM.channelClickId.key,
                        channelHelp.PINTEREST_URL_PARAMS_ENUM.channelClickId.key,
                        "ttclid",
                    ],
                }
                let deep_link_sub10 = {
                    paramKey: "deep_link_sub10",
                    keys: [
                        // "", // facebook没有这个值，所以这里直接传空
                        // "af_adset_id", 
                        channelHelp.PUBLIC_URL_PARAMS_ENUM.adSetId.key,
                    ], 
                }
                let custom_ss_ui = {
                    paramKey: "af_ss_ui",
                    defaultValue: "true",
                }
                let af_r = {
                    paramKey: "af_r",
                    keys: ["af_r","af_r", "af_r",],
                }

                let params = {
                    oneLinkURL: window.GloablEnvConfig.oneLinkURL,
                    afParameters: {
                        mediaSource: mediaSource,
                        campaign: campaign,
                        afSub1: afSub1,
                        googleClickIdKey: googleClickIdKey,
                        deepLinkValue: deepLinkValue,
                        afCustom: [
                            deep_link_sub1,
                            af_campaignid,
                            deep_link_sub2,
                            deep_link_sub3,
                            deep_link_sub4,
                            deep_link_sub5,
                            deep_link_sub6,
                            deep_link_sub7,
                            deep_link_sub8,
                            deep_link_sub9,
                            deep_link_sub10, // 新增参数
                            af_r,
                            custom_ss_ui,
                        ],
                        ad: ad,
                        adSet: adSet,
                    }
                }
                return params
            },
            
            // Google归因
            归因gogole() {
                const routerQuery = this.getRouteQuery()
                //正在初始化智能脚本参数
                //Initializing Smart Script arguments
                let mediaSource = {
                    keys: ["pid"],
                    defaultValue: ChannelChange.getSource(),
                }
                let campaign = {
                    keys: ["c"],
                    defaultValue: "campaign_name",
                }
                let af_cid = {
                    keys: ["af_cid"],
                }
                let ad = {
                    keys: ["af_ad"],
                    defaultValue: "ad",
                }
                let googleClickIdKey = "af_sub1"
                let afSub2 = {
                    keys: ["gbraid"],
                }
                let afSub3 = {
                    keys: ["wbraid"],
                }
                let afSub4 = {
                    keys: ["json"],
                    defaultValue: JSON.stringify({}),
                }
                let channel = {
                    keys: ["af_channel"],
                }
                let af_keywords = {
                    paramKey: "af_keywords",
                    keys: ["af_keywords"],
                }
                let af_siteid = {
                    paramKey: "af_siteid",
                    keys: ["af_siteid"],
                }
                let afSub5 = {
                    keys: ["ad_set"],
                }
                let deepLinkValue = {
                    keys: ["bookid"],
                }
                let custom_ss_ui = {
                    paramKey: "af_ss_ui",
                    defaultValue: "true",
                }
                let af_r = {
                    paramKey: "af_r",
                    keys: ["af_r"],
                }

                let afSub4Object = {}
                let afSub4Param = routerQuery.json
                let cid = routerQuery.af_cid
                let packageName = routerQuery.packageName
                let book_id = routerQuery.book_id
                let book_version = routerQuery.book_version
                let book_version_name = routerQuery.book_version_name
                let chapter_id = routerQuery.chapter_id

                if (afSub4Param && "" !== afSub4Param.trim()) {
                    afSub4Object = JSON.parse(afSub4Param)
                }
                if (cid && "" !== cid.trim() && "{campaignid}" !== cid.trim()) {
                    afSub4Object.af_cid = cid
                }
                if (book_id && book_id > 0) {
                    afSub4Object.book_id = book_id
                }
                if (book_version && book_version >= 0) {
                    afSub4Object.book_version = book_version
                }
                if (book_version_name && "" !== book_version_name.trim()) {
                    afSub4Object.book_version_name = book_version_name
                }
                if (chapter_id && chapter_id > 0) {
                    afSub4Object.chapter_id = chapter_id
                }

                // if (undefined !== afSub4Object) {
                //     console.log("格式化afSub4Object")
                //     afSub4 = JSON.stringify(afSub4Object)
                // }

                // if ("{}" == afSub4) {
                //     afSub4 = { keys: ["json"] }
                // }
                const isObject = (e) => Object.prototype.toString.call(e) === "[object Object]"
                if (!isObject(afSub4Object)) {
                    console.error('归因 afSub4 不是对象', afSub4Object)
                }
                console.log(`log 调试数据: format afSub4.defaultValue __ 1`, afSub4.defaultValue)
                afSub4.defaultValue = JSON.stringify(afSub4Object)
                console.log(`log 调试数据: format afSub4.defaultValue __ 2`, afSub4.defaultValue)
                console.log(`log 调试数据: format afSub4.defaultValue __ 3`, JSON.parse(afSub4.defaultValue))

                let params = {
                    oneLinkURL: window.GloablEnvConfig.oneLinkURL,
                    afParameters: {
                        mediaSource: mediaSource,
                        campaign: campaign,
                        ad: ad,
                        googleClickIdKey: googleClickIdKey,
                        afSub2: afSub2,
                        afSub3: afSub3,
                        afSub4: afSub4,
                        channel: channel,
                        afCustom: [
                            af_keywords,
                            af_siteid,
                            af_cid,
                            custom_ss_ui,
                        ],
                        afSub5: afSub5,
                        deepLinkValue: deepLinkValue,
                        af_r,
                    },
                }
                return params

            },

            // 生成归因链接
            fireImpressionsLink() {
                let params = {}
                if (ChannelChange.isFacebook) {
                    params = this.归因facebook()
                    console.log("🚀 ~ fireImpressionsLink ~ 生成 Facebook 归因  :", params)
                }
                if (ChannelChange.isTiktok) {
                    params = this.归因tiktok()
                    console.log("🚀 ~ fireImpressionsLink ~ 生成 Tiktok 归因  :", params)
                }
                if (ChannelChange.isSnapchat) {
                    params = this.归因snapchat()
                    console.log("🚀 ~ fireImpressionsLink ~ 生成 Snapchat 归因  :", params)
                }
                if (ChannelChange.isPinterest) {
                    params = this.归因pinterest()
                    console.log("🚀 ~ fireImpressionsLink ~ 生成 Pinterest 归因  :", params)
                }
                if (ChannelChange.isGoogle) {
                    params = this.归因gogole()
                    console.log("🚀 ~ fireImpressionsLink ~ 生成 gogole 归因 :", params)
                }

                //Call the function after embedding the code through a global parameter on the window object called window.AF_SMART_SCRIPT.
                //Onelink URL is generated.
                let result = window.AF_SMART_SCRIPT.generateOneLinkURL(params)

                let result_url = "No output from script"
                if (result) {
                    result_url = result.clickURL
                    // console.log("🚀 ~ 生成归因链接是: ~ result_url 之前:", result_url)
                    channelHelp.logUrlParams('生成归因链接是: ~ result_url 之前', result_url)

                    // #region 因为会无缘无故 pid=fb 按正常来说 pid=url上的source的值，现在这里直接强制重写
                    let newUrl = new URL(result_url)
                    let paramSourceValue = getUrlParams('source') // 获取拿 result_url.searchParams.get('deep_link_sub3') 因为和 url上的source 值是一样的
                    newUrl.searchParams.set('pid', paramSourceValue)
                    result_url = newUrl.href
                    // #endregion

                    // console.log("🚀 ~ 生成归因链接是: ~ result_url 之后:", result_url)
                    channelHelp.logUrlParams('生成归因链接是: ~ result_url 之后', result_url)
                }
                // console.log("🚀 ~ 生成归因链接是: ~ result_url 最终:", result_url)
                channelHelp.logUrlParams('生成归因链接是: ~ result_url 最终', result_url)
                //将OneLink智能脚本生成的URL赋值this.oneLinkURLGO  跳转时用到
                this.oneLinkURLGO = result_url
                //https://afbasicapp.onelink.me/CJRS?af_js_web=true&af_ss_ver=2_2_0&pid=default_media_source&c=default_campaign&af_ss_ui=true
            },

            getPopleBubbleHeight() {
                let className
                // 根据不同的类型，获取不同的元素
                if (this.bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.图片) {   
                    className = 'pople__bubble--img'
                } else {
                    className = 'pople__html'
                }
                let elementPopleBubble = document.querySelector(`.${className}`)
                if (elementPopleBubble) {
                    return elementPopleBubble.offsetHeight
                }
            },
            // 占据高度作用
            setDivSizeBox() {
                const div = document.querySelector('.sizebox')
                const height = this.getPopleBubbleHeight()
                let setHeight = ''
                if (this.bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.图片) {   
                    // 图片是定位的，所以需要高度的支撑
                    setHeight = `${height}px`
                    console.log('setDivSizeBox -- 图片')
                } else if (this.bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.文字) {
                    // 文字是直接显示在文章内容内容的最下面，所以不需要高度的支撑
                    setHeight = `0px`
                    console.log('setDivSizeBox -- 文字')
                }
                console.log("🚀 ~ setDivSizeBox ~ book:", this.bookData)
                console.log("🚀 ~ setDivSizeBox ~ type:", this.bookData.transGuideCssType)
                console.log("🚀 ~ setDivSizeBox ~ setHeight:", setHeight)
                div.style.height = setHeight
            },

            getChapterContentHeight() {
                let height
                if (this.bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.图片) {   
                    let element = document.querySelector('.pople__img')
                    if (element) {
                        height = element.offsetHeight * 0.5 // 判断一半距离就算到底部
                    }
                } else if (this.bookData.transGuideCssType === PopularizeFetchEnum.innterhtmlTypeNum.文字) {
                    let element = document.querySelector('.pople__html')
                    if (element) {
                        height = element.offsetHeight * 0.5 // 判断一半距离就算到底部
                    }
                }
                else {
                    height = 10 // 默认10px
                }
                return height
            },

            // 滚动处理
            handleScroll() {
                // 获取视口高度
                const viewportHeight = window.innerHeight;
                // 获取页面总高度
                const documentHeight = Math.max(
                    document.body.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.clientHeight,
                    document.documentElement.scrollHeight,
                    document.documentElement.offsetHeight
                );
                // 获取滚动位置
                const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
                
                // 计算是否滚动到底部（添加一个小的阈值，避免精度问题）
                const threshold = this.getChapterContentHeight(); // 阈值
                const isAtBottom = (viewportHeight + scrollPosition) >= (documentHeight - threshold);
                
                if (nameConfig.isDevScript) {
                    // console.log("🚀 ~ 滚动检测 ~ viewportHeight:", viewportHeight);
                    // console.log("🚀 ~ 滚动检测 ~ scrollPosition:", scrollPosition);
                    // console.log("🚀 ~ 滚动检测 ~ documentHeight:", documentHeight);
                    // console.log("🚀 ~ 滚动检测 ~ 是否到底:", isAtBottom);
                }

                this.showBubble = isAtBottom;
                if (nameConfig.isDevScript && this.showBubble) {
                    console.log("🚀 ~ handleScroll ~ check 😄");
                    console.log("🚀 ~ handleScroll ~ 滚动到底部了");
                    console.log("🚀 ~ handleScroll ~ check 😄");
                }

                if (this.showBubble && this.initPage) {
                    ChannelChange.viewContentFinishChangleTrack();
                    this.数数打点("read_completed");
                    this.initPage = false;
                }
            },

            // 创建SEO相关的meta标签
            createScriptMeta(data) {
                document.title = data.bookName

                let metaTitle = document.createElement("meta")
                metaTitle.name = "og:title"
                metaTitle.property = "og:title"
                metaTitle.content = data.bookName
                document.head.appendChild(metaTitle)

                let metaDescription = document.createElement("meta")
                metaDescription.name = "og:description"
                metaDescription.property = "og:description"
                metaDescription.content = data.description
                document.head.appendChild(metaDescription)

                if (data.cover) {
                    let metaImg = document.createElement("meta")
                    metaImg.name = "og:image"
                    metaImg.property = "og:image"
                    metaImg.content = data.cover
                    document.head.appendChild(metaImg)
                }
            },

            // 获取小说详情数据
            getBookDetail() {
                // http://dev.cms.unbing.cn/swagger-ui.html?urls.primaryName=Yestory-Ebook-Web-Api#/book-controller
                const eventDto = {
                    ...this.shushuDeviceMsg,
                    eventType: "sharepage_open",
                }
                let require = {
                    url: "api/web/ebook/book/popularize_book",
                    method: "post",
                }
                let params = {
                    ...this.getRouteQuery(),
                    eventDto,
                }
                this.$httpService(require, params).then(async (data) => {
                    // 处理小说的详细内容
                    data.popularizeChapterDetails.map((element) => {
                        element.chapterContent = element.chapterContent
                            .split("<br>")
                            .filter((item) => item.trim() !== "")
                        return element
                    })
                    //
                    this.bookData = data

                    // 是否从广告数据点击进来的
                    let isAutoLocation = Number(this.bookData.style) === 3
                    this.adsModel = isAutoLocation
                    window.bookData = this.bookData
                    console.log("🚀 ~ ).then ~ this.bookData:", this.bookData)

                    this.loaded = true
                    //this.bookData.shareChaperDetail.chapterContent = data.shareChaperDetail.chapterContent.replace(/\n+/g, "\n").split("\n").filter(item => item.trim() !== "");
                    if (this.bookData != undefined) {
                        console.log(`log 调试数据: test 1`)

                        this.createScriptMeta(data)
                        console.log(`log 调试数据: test 2`)

                        ChannelChange.publicPixedlIdInit(this.bookData.pixelId)
                        console.log(`log 调试数据: test 3`)

                        // 获取 ajax 后重新生成 归因
                        console.log(`log 调试数据: test 4`)

                        this.buildAtf()
                        console.log(`log 调试数据: test 5`)

                        this.数数打点("page_finish")
                        console.log(`log 调试数据: test 6`)


                        let {
                            isNaturalQuantity,
                            isBuyUserTypeUrlParam,
                        } = this.getUserUtmType()
                        if (isNaturalQuantity) {
                            this.clickIpAttribution()
                            return
                        }

                        /**
                         * 等待广告平台Cookie加载完成后再上报归因数据
                         * 支持Facebook、TikTok、Pinterest三个平台
                         */
                        await this.waitForTrackingCookies()
                        this.clickIpAttribution()
                    }
                })
            },


            数数打点(eventType) {
                console.log("🚀 ~ eventType:", eventType)
                let query = this.getRouteQuery()
                let bookData = this.bookData
                let bookVersionName = bookData.versionName || query.book_version_name

                let source = ChannelChange.getSource()
                let style = bookData.style || 1
                let bookVersion = bookData.version || 0
                let bookId = bookData.bookId || query.book_id
                let maxchapter = bookData.chapterNumber || query.chapter_id || 0

                let campaign
                let campaignid
                let adname
                let adsetname


                let gclid 
                let gbraid
                let wbraid
                /* if (ChannelChange.isFacebook) {
                    campaign = query[channelHelp.FACEBOOK_URL_PARAMS_ENUM.campaignName.key]
                    campaignid = query[channelHelp.FACEBOOK_URL_PARAMS_ENUM.campaignId.key]
                    adname = query[channelHelp.FACEBOOK_URL_PARAMS_ENUM.adVertisementName.key]
                    adsetname = query[channelHelp.FACEBOOK_URL_PARAMS_ENUM.adSetName.key]
                } */
                if (ChannelChange.isFacebook || ChannelChange.isSnapchat || ChannelChange.isPinterest) {
                    // 广告系列名称
                    campaign = query[channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignName.key]
                    // 广告系列ID
                    campaignid = query[channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignId.key]
                    // 广告名称
                    adname = query[channelHelp.PUBLIC_URL_PARAMS_ENUM.adVertisementName.key]
                    // 广告组名称
                    adsetname = query[channelHelp.PUBLIC_URL_PARAMS_ENUM.adSetName.key]
                }
                if (ChannelChange.isTiktok) {
                    campaign = query.af_c // 对应的是渠道名
                    campaignid = query.af_c_id // 对应的是渠道ID
                    adname = query.af_ad
                    adsetname = query.af_adset
                }
                if (ChannelChange.isGoogle) {
                    campaign = query.c
                    campaignid = query.af_cid
                    adname = query.af_ad
                    adsetname = query.af_adset

                    gclid = query.gclid
                    gbraid = query.gbraid
                    wbraid = query.wbraid
                }

                ChannelChange.logAllChannelClickId()

                let af_r = this.dataAfR || query.af_r || ''
                // let pixelId = bookData.pixelId || query.pixelId
                let pixelId = ChannelChange.getPixedId()
                    console.log("🚀 ~ pixelId:", pixelId)

                let mccId = bookData.mccId || query.mccId || ''

                let pointData = {
                    bookId: bookId, // 小说ID
                    campaign: campaign, // 广告系列名称
                    source: source, // 渠道
                    maxchapter: maxchapter, // 章节数
                    style: style, // 小说样式
                    book_version: bookVersion, // 小说版本
                    book_version_name: bookVersionName, // 小说版本名称
                    campaign_id: campaignid, // 广告系列ID
                    ad_name: adname, // 广告名称
                    adset_name: adsetname, // 广告组名称
                    af_r: af_r, // 广告落地页

                    pixelId: pixelId, // 像素ID
                    mccId: mccId,

                    url: location.href, // 当前页面URL
                }
                if (eventType === "btn_click") {
                    pointData.from = this.clickButtonType
                }
                if (eventType === 'view_time') {
                    pointData.view_time = this.browseTime
                }
                if (eventType === 'page_finish') {
                    this.adsPageDataLoaded.push(1)
                }

                this.$ta.track(eventType, pointData)
                //
                this.$ta.userSet({ campaign: campaign, })
                this.$ta.userSet({ campaign_id: campaignid, })
                this.$ta.userSet({ ad_name: adname, })
                this.$ta.userSet({ adset_name: adsetname, })
            },

            /**
             * 等待广告平台跟踪Cookie加载完成后补充调用归因
             * 支持Facebook、TikTok、Pinterest平台
             * 注意：这里会在Cookie加载完成后再次调用 clickIpAttribution()
             */
            async waitForTrackingCookies() {
                const logPrefix = '[waitForTrackingCookies]'

                // 使用ChannelChange中的统一方法获取平台信息
                const platformName = ChannelChange.getCurrentPlatformName()
                if (platformName === 'Unknown') {
                    return // 不是支持的平台，直接返回
                }

                this.loopData[0] = new Date().toLocaleString()
                console.log(`${logPrefix} ${platformName}平台开始轮询查询Cookie...`)

                // 立即检查一次
                if (ChannelChange.checkTrackingCookies()) {
                    this.loopData[1] = new Date().toLocaleString()
                    console.log(`${logPrefix} Cookie已存在，无需等待 [拿到cookie方式1~马上拿到] ${ChannelChange.getCurrentPlatformCookiesLog()}`)
                    return
                }

                // 如果没有Cookie，启动轮询（不等待Promise完成，立即返回）
                console.log(`${logPrefix} 未拿到Cookie，启动轮询...`)

                this.loopTime = setInterval(() => {
                    console.log(`${logPrefix} Cookie轮询查询中... ${ChannelChange.getCurrentPlatformCookiesLog()}`)

                    if (ChannelChange.checkTrackingCookies()) {
                        clearInterval(this.loopTime)
                        this.loopData[1] = new Date().toLocaleString()
                        console.log(`${logPrefix} Cookie加载完成 [拿到cookie方式2～轮询方式拿到] ${ChannelChange.getCurrentPlatformCookiesLog()}`)
                        // 重要：Cookie加载完成后再次调用归因
                        this.clickIpAttribution()
                    }
                }, 100)

                // 立即返回，不等待轮询完成（保持原始逻辑）
                return
            },

            // 获取 ajax 后重新生成 归因
            buildAtf() {
                // 最终值
                let af_r_value
                //  object 缓存的 url 多个
                const jumpUrlMap = this.bookData.jumpUrlMap
                //  string 默认的 ulr 单个
                const jumpDefaultUrl = this.bookData.jumpDefaultUrl
                const cache_af_r_keys = Object.keys(jumpUrlMap)

                let aftCacheInfo = store.state.user.afr
                console.log("🚀 ~ buildAtf ~ aftCacheInfo:", aftCacheInfo)
                const cpm = this.$route.query.cpm
                let cacheKey = cpm
                let cacheKeyValue

                //先查 vuex 缓存
                if (aftCacheInfo && aftCacheInfo.afrKey && aftCacheInfo.userCpmKey && cpm) {
                    // 路由参数 与 vuex 中的 key 相同情况下 用缓存的数据
                    if (cacheKey == aftCacheInfo.userCpmKey) {
                        console.log(`log 调试数据: arf 第 2 次获取`)
                        let afrUrlKey = aftCacheInfo.afrKey
                        if (
                            'default' ==afrUrlKey
                            || undefined == jumpUrlMap
                            || jumpUrlMap==null
                            || undefined== jumpUrlMap[afrUrlKey]
                            || null== jumpUrlMap[afrUrlKey]
                            || '' == jumpUrlMap[afrUrlKey]
                        ) {
                            this.dataAfR = jumpDefaultUrl;
                        } else {
                            this.dataAfR = jumpUrlMap[afrUrlKey];
                        }
                    }
                }

                //没有缓存，随机jump map key
                //   if (undefined== this.dataAfR || null==this.dataAfR && "" == this.dataAfR.trim()) {
                if (checkNotValue(this.dataAfR)) {
                    console.log(`log 调试数据: arf 第 1 次获取`)
                    // 随机数 返回 index 下标
                    const randowm_between = (array) => {
                        let randomNum = Math.ceil(Math.random() * 100)
                        let percentage = Math.round(
                            100 / array.length
                        ).toFixed(0)
                        let randomIndex = Math.floor(
                            randomNum / percentage
                        ).toFixed(0)
                        return randomIndex
                    }

                    // 如果存在缓存就拿缓存的值    随机获取
                    if (cache_af_r_keys.length) {
                        const index = randowm_between(cache_af_r_keys)
                        cacheKeyValue= cache_af_r_keys[index]
                        const value = jumpUrlMap[cacheKeyValue]
                        af_r_value = value
                    } else {
                        // 没有则用默认的
                        cacheKeyValue='default';
                        af_r_value = jumpDefaultUrl
                    }
                    // 赋值
                    this.dataAfR = af_r_value
                    let aftCacheInfo = {
                        // 用户缓存 key [直接获取的是url的参数,固定]
                        userCpmKey: cacheKey,
                        // 随机或默认 key
                        afrKey: cacheKeyValue
                    }
                    store.dispatch("user/setAfr", aftCacheInfo)

                    console.log("🚀 ~ buildAtf ~ this.bookData:", this.bookData)
                    console.log("🚀 ~ buildAtf ~ af_r_value:", af_r_value)
                    console.log("🚀 ~ buildAtf ~ this.dataAfR:", this.dataAfR)
                }



                // 组装新onelink
                if (this.dataAfR && "" != this.dataAfR.trim()) {
                    const newQuery = this.getRouteQuery()
                    let afRUrl = newQuery.af_r
                    let a1 = this.dataAfR && this.dataAfR.trim()
                    let a2 = afRUrl && decodeURIComponent(afRUrl.trim())
                    if (a1 !== a2) {
                        console.log("🚀 ~ buildAtf ~ this.getRouteQuery():", this.getRouteQuery())
                        this.$router.push({
                            query: {
                                ...newQuery,
                                af_r: this.dataAfR.trim(),
                            }
                        })
                    }
                    // let afRUrl = this.$route.query.af_r
                    // if (
                    //     afRUrl
                    //     || "" == afRUrl.trim()
                    //     || this.dataAfR.trim() != decodeURIComponent(afRUrl.trim())
                    // ) {
                    //     this.$router.push({
                    //         query: {
                    //             ...this.getRouteQuery(),
                    //             af_r: this.dataAfR.trim(),
                    //         }
                    //     })
                    // }


                    let returnUrl = this.oneLinkURLGO
                    console.log("🚀 ~ buildAtf ~ returnUrl 1:", this.oneLinkURLGO)
                    if (returnUrl.indexOf("?") != -1) {
                        let str = returnUrl.substr(returnUrl.indexOf("?") + 1)
                        let strs = str.split("&")
                        let replaceUrl = false
                        if (strs && strs.length > 0) {
                            let returnUrlDomail = returnUrl.substr(
                                0,
                                returnUrl.indexOf("?")
                            )
                            let returnParamsNew = ""
                            for (let i = 0; i < strs.length; i++) {
                                let returnParams = strs[i].split("=")
                                let returnParamsKey = returnParams[0]
                                // let returnParamValue=decodeURIComponent(returnParams[1]);
                                if (returnParamsNew != "") {
                                    returnParamsNew = returnParamsNew + "&"
                                }
                                if (returnParamsKey && "af_r" === returnParamsKey.trim()) {
                                    if (this.dataAfR.trim() !== decodeURIComponent(returnParams[1])) {
                                        returnParamsNew =
                                            returnParamsNew +
                                            returnParamsKey +
                                            "=" +
                                            encodeURIComponent(this.dataAfR)
                                        replaceUrl = true
                                    }
                                } else if (returnParamsKey && "af_r" != returnParamsKey.trim()) {
                                    returnParamsNew =
                                        returnParamsNew +
                                        returnParamsKey +
                                        "=" +
                                        returnParams[1]
                                }
                            }
                            if (!replaceUrl) {
                                returnParamsNew =
                                    returnParamsNew +
                                    "&af_r=" +
                                    encodeURIComponent(this.dataAfR)
                            }
                            this.oneLinkURLGO =
                                returnUrlDomail + "?" + returnParamsNew

                            this.adsPageDataLoaded.push(1)
                        }
                    }
                    console.log("🚀 ~ buildAtf ~ returnUrl 2:", this.oneLinkURLGO )
                }
            },

            // 归因 上报 服务端
            ipFetch(newQuery, clickIpAttributionDomain,) {
                console.log("🚀 ~ ipFetch ~ newQuery, clickIpAttributionDomain:", newQuery, clickIpAttributionDomain)
                if (this.$route.query.debug && nameConfig.isDevScript) {
                    console.log("🚀 ~ ipFetch ~ 调试模式，不进行归因上报")
                    return
                }

                //如果有引入 lodash, 可以写成: let newQuery = _.omit(query, 'code')
                axios({
                    method: "get",
                    params: newQuery,
                    url: clickIpAttributionDomain,
                })
                    .then((result) => {
                        console.log(
                            "ipFetch success",
                            result
                        )
                        if (result.data) {
                            let status = result.data.status
                            // console.log("ipFetch status:",status);
                            if (1 === status) {
                                console.log(
                                    "ipFetch success"
                                )
                            } else {
                                console.error(
                                    "ipFetch fail:",
                                    result.msg
                                )
                            }
                        }
                    })
                    .catch((err) => {
                        console.error(err)
                    })
            },
            /**
             * 获取用户买量类型
             * isNaturalQuantity     boolean 是否自然量
             * isBuyUserTypeUrlParam boolean 是否买量
             */
            getUserUtmType() {
                const query = this.getRouteQuery()
                let checkCampaignName = query[channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignName.key]
                let checkUtmCampaignId = query[channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignId.key]
                let check1 = Object.values(channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignName.channelDefaultsValue).includes(checkCampaignName)
                let check2 = Object.values(channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignId.channelDefaultsValue).includes(checkUtmCampaignId)
                let isNaturalQuantity = check1 && check2
                console.log("🚀 ~ getUserUtmType ~ 是自然量吗！！:", isNaturalQuantity)
                let isBuyUserTypeUrlParam = !isNaturalQuantity
                console.log("🚀 ~ getUserUtmType ~ 是买量吗！！:", isBuyUserTypeUrlParam)
                return {
                    isNaturalQuantity,
                    isBuyUserTypeUrlParam,
                }
            },

            // ip 请求，不知道啥用
            // 上报归因结果
            clickIpAttribution() {
                const query = this.getRouteQuery()

                try {
                    const clickIpAttributionDomain =
                        window.GloablEnvConfig.VUE_APP_CLICK_IP_ATTRIBUTION_DOMAIN // 归因上报地址
                    const APP_THINKDATA_APP_ID_JSON =
                        window.GloablEnvConfig.VUE_APP_THINKDATA_APP_ID_JSON

                    if (clickIpAttributionDomain && query) {
                        /**
                         * 买量用户 
                         * -- 通过归因投放新增的用户，比如归因到facebook、google
                         * -- 如果没有归因的就是自然用户，花钱获客和没有花钱就用户过来的区
                         * -1 普通用户 【正常的用户】
                         * 1  付费用户 【通过投放广告过来的用户】
                         */
                        let userFrom = -1 // 用户类型
                        // if (ChannelChange.isFacebook) {
                            let fbclid = query.fbclid // 测试环境没有这个参数，正式环境经过 facebook 才会有
                            let utmCampaign = query.utm_campaign
                            let utmCampaignid = query.utm_campaignid // facebook 的  广告系列的唯一ID（注意不同的平台key都不一样）
                        // }
                        // else if (ChannelChange.isTiktok) {
                            let ttclid = query.ttclid // 测试环境没有这个参数，正式环境经过 tiktok 才会有
                            let af_c = query.af_c
                            let af_c_id = query.af_c_id
                        // }
                        // else if (ChannelChange.isSnapchat) {
                        // }
                        // if (ChannelChange.isGoogle) {
                            let gclid = query.gclid
                            let gbraid = query.gbraid
                            let wbraid = query.wbraid
                            let mccId = query.mccId
                            let campaign = query.c
                            let campaignid = query.af_cid
                        // }
                        let newQuery = JSON.parse(
                            JSON.stringify(query)
                        ) // 深拷贝\
                        delete newQuery.stop
                        delete newQuery.testSecretKey
                        delete newQuery.packageName

                        let bookVersion = 0
                        let pkgName = query.packageName
                        let thinkDataAppIdInfo = []
                        thinkDataAppIdInfo = JSON.parse(
                            APP_THINKDATA_APP_ID_JSON
                        )
                        console.log("🚀 ~ clickIpAttribution ~ thinkDataAppIdInfo:", thinkDataAppIdInfo)
                        if (thinkDataAppIdInfo &&thinkDataAppIdInfo.length > 0) {
                            for (let index in thinkDataAppIdInfo) {
                                let configPackgeName =
                                    thinkDataAppIdInfo[index].packgeName
                                let appleId = thinkDataAppIdInfo[index].appleId
                                let configOs = thinkDataAppIdInfo[index].os
                                if (
                                    configPackgeName == pkgName 
                                    && configOs === "ios" 
                                    && appleId
                                ) {
                                    pkgName = appleId
                                    break
                                }
                            }
                        }
                        console.log("🚀 ~ clickIpAttribution ~ pkgName:", pkgName)
                        newQuery.pkgName = pkgName

                        if (ChannelChange.isGoogle) {
                            if (
                                this.bookData.mccId
                                && "" != this.bookData.mccId.trim()
                                && "" != this.bookData.mccId > 0
                            ) {
                                mccId = this.bookData.mccId
                                console.log("click bookData mccId:",mccId)
                                newQuery.mccId = mccId
                            }
                        }

                        if (this.bookData.version) {
                            bookVersion = this.bookData.version
                        }

                        newQuery.source = ChannelChange.getSource()
                        newQuery.book_version = bookVersion

                        // 是否 Facebook 买量用户
                        /* let isFacebookBuyUserType = fbclid && utmCampaign !==  '{{campaign.name}}' && utmCampaignid !== '{{campaign.id}}'
                        if (ChannelChange.isFacebook && isFacebookBuyUserType) {
                            userFrom = 1 // 买量用户
                        } */
                        // 是否 Tiktok 买量用户
                        let isTiktokBuyUserType = ttclid !== '__CLICKID__'&& af_c !== '__CAMPAIGN_NAME__' && af_c_id !== '__CAMPAIGN_ID__'
                        if (ChannelChange.isTiktok && isTiktokBuyUserType) {
                            userFrom = 1 // 买量用户
                        }

                         // 自然辆没有这个参数，正式买量经过 facebook 才会有
                        let facebookChannelClickId = query[channelHelp.FACEBOOK_URL_PARAMS_ENUM.channelClickId.key]
                        // 自然辆没有这个参数，正式买量经过 snapchat 才会有
                        let snapChatChannelClickId = query[channelHelp.SNAPCHAT_URL_PARAMS_ENUM.channelClickId.key]
                        // 自然辆没有这个参数，正式买量经过 pinterest 才会有, 这里是从 cookie 中获取的
                        let pinterestChannelClickId = ChannelChange.getPinterestClickId()

                        /**
                         * 判断买量用户
                         */
                        // 
                        let { isBuyUserTypeUrlParam } = this.getUserUtmType()
                        if (isBuyUserTypeUrlParam) {
                            if (ChannelChange.isFacebook) {
                                if (facebookChannelClickId) {
                                    userFrom = 1 // 买量用户
                                }
                            }
                            // 是否 Snapchat 买量用户
                            if (ChannelChange.isSnapchat) {
                                if (snapChatChannelClickId) {
                                    userFrom = 1 // 买量用户
                                }
                            }
                            // 是否 Pinterest 买量用户
                            if (ChannelChange.isPinterest) {
                                if (pinterestChannelClickId) {
                                    userFrom = 1 // 买量用户
                                }
                            }
                        }

                        if (ChannelChange.isGoogle) {
                            let isCheckUserFrom = false

                            console.log("🚀 ~ clickIpAttribution ~ gclid:", gclid)
                            if (gclid && "" != gclid.trim()) {
                                isCheckUserFrom = true
                            } else if (
                                gbraid && "" != gbraid.trim()
                                &&
                                wbraid && "" != wbraid.trim()){
                                isCheckUserFrom = true
                            }

                            if (
                                campaign && "{campaignid}" != campaign
                                && campaignid && "" != campaignid.trim()
                                && "{campaignid}" != campaignid
                                && isCheckUserFrom
                            ) {
                                userFrom = 1
                            }
                        }

                        if (ChannelChange.isFacebook || ChannelChange.isSnapchat || ChannelChange.isPinterest) {
                            newQuery.actionSource = "website"
                        }
                        newQuery.userFrom = userFrom // 用户类型
                        newQuery.os = uaOs.getUaOs(); // 操作系统
                        newQuery.ts = new Date().getTime() // 时间戳
                        newQuery.appVersion=window.GloablEnvConfig.VUE_APP_VERSION; // 版本号
                        //TODO 识别浏览系统版本
                        newQuery.systemVersion = uaOs.getNavigatorAbout().version // 浏览器版本
                        console.log("🚀 ~ clickIpAttribution ~ newQuery:", newQuery)

                        let bookId = query.book_id

                        if (this.bookData.bookId) {
                            bookId = this.bookData.bookId
                        }

                        if (ChannelChange.isFacebook || ChannelChange.isSnapchat || ChannelChange.isPinterest) {
                            newQuery.pixelId = ChannelChange.currentPixelId
                            newQuery.externalThinkDataId = ChannelChange.getExternalThinkDataId()
                            newQuery.externalId = bookId
                        }
                        if (ChannelChange.isFacebook) {
                            const facebookCookies = ChannelChange.getFacebookCookies()
                            newQuery.fbp = facebookCookies.fbp
                            newQuery.fbc = facebookCookies.fbc
                        }
                        if (ChannelChange.isPinterest) {
                            newQuery.platformClickId = pinterestChannelClickId // 从 cookie 上拿的
                        }
                        if (ChannelChange.isGoogle) {
                            newQuery.book_id = bookId
                        }

                        let run归因 = () => {
                            this.ipFetch(newQuery, clickIpAttributionDomain)
                        }
                        // 产品要求的
                        // if (ChannelChange.isTiktok) {
                        //     setTimeout(() => {
                        //         run归因()
                        //     }, 2000)
                        // } else {
                        // }
                        run归因()
                    }
                } catch (err) {
                    console.error(err)
                }
            },

            // 【ctrl + v 剪贴板】复制 url 的内容到 剪贴板  [ios或android需要拿到的，拿到的如书本id，根据id跳转页面]
            copyUrlQuery() {
                const newQuery = this.getRouteQuery()
                delete newQuery.stop
                delete newQuery.testSecretKey
                newQuery.source = ChannelChange.getSource()
                // 客户端只识别 utm 开头的归因，这里如果是 tiktok 的话，将 af 相关的映射到 utm 相关的
                if (ChannelChange.isTiktok) {
                    let afMap = [
                        [channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignName.key,"af_c",],
                        [channelHelp.PUBLIC_URL_PARAMS_ENUM.campaignId.key,"af_c_id",],
                        [channelHelp.PUBLIC_URL_PARAMS_ENUM.adSetName.key, "af_adset",],
                        [channelHelp.PUBLIC_URL_PARAMS_ENUM.adVertisementName.key,"af_ad",],
                    ]
                    for (const arr of afMap) {
                        let keyInFacebook = arr[0]
                        let keyInTiktok =   arr[1]
                        newQuery[keyInFacebook] = newQuery[keyInTiktok]
                    }
                }
                console.log("🚀 ~ copyUrlQuery ~ newQuery:", newQuery)
                this.clipboardTxt = JSON.stringify(newQuery)

                // const newQuery = this.getRouteQuery()
                // let ob = {
                //     ...newQuery,
                //     source: getSource(newQuery),
                // }
                // this.clipboardTxt = JSON.stringify(ob)
            },

            // 页面初始化
            __init() {
                this.initPage = true
                this.数数打点("page_view")
                this.getBookDetail()
                //设置定时器
                this.timeInterval = setInterval(() => {
                    this.browseTime++
                }, 1000)
            },

            fetchBeforeInit() {
                ChannelChange.setSource(this.getRouteQuery().channel)
                console.log(`log 调试数据: this.getRouteQuery()`, this.getRouteQuery());
                ChannelChange.autoImportChanelJs(this.getRouteQuery().pixelId)
                this.copyUrlQuery()
            },

            pageInit() {
                // 是否是 debug 模式 或者 开发环境
                if (this.$route.query.debug || nameConfig.isDevScript) {
                    channelHelp.channelRegisterAddEvent()
                }
                // 判断渠道
                if (this.$route.query.debug) {
                    Object.assign(this.$data, mockObject) // debug 模式，自己填充数据

                    // 页面初始化调用
                    this.fetchBeforeInit()
                    this.数数打点("page_view")

                    this.buildAtf()
                    ChannelChange.publicPixedlIdInit(this.bookData.pixelId)
                    this.数数打点("page_finish")

                    this.clickIpAttribution() // 归因上报
                } else {
                    this.fetchBeforeInit()

                    this.__init()
                }
            }
        },

        created() {
            this.pageInit()
        },

        mounted() {
            window.addEventListener("scroll", this.handleScroll)
            window.addEventListener("beforeunload", (e) =>
                this.beforeunloadHandler(e)
            )
            this.fireImpressionsLink()
        },

        beforeDestroy() {
            window.removeEventListener("scroll", this.handleScroll)
            window.removeEventListener("beforeunload", (e) =>
                this.beforeunloadHandler(e)
            )
        },

        beforeRouteLeave(to, from, next) {
            this.beforeunloadHandler()
            next()
        }
    }
</script>
<style>
.content__ritch img {
    width: 100%;
    height: 100%;
}
</style>
<style lang="scss" scoped>
    .skeleton__animate__img {
        position: absolute;
        width: 106px;
        top: -40px;
        height: calc(328px  + 40px);
        transform: rotate(-20deg);
        left: -3%;
        transform-origin: top left;
    }

    .skeleton__animate__img--1 {
        animation: skeleton__animate__move1 2.5s ease-in-out infinite;
    }

    @keyframes skeleton__animate__move1 {
        0% {
            left: -3%;
        }
        40% {
            left: 100%;
        }
        100% {
            left: 100%;
        }
    }

    $popImageHeight: 212px; // 图片高度
    $maskHeight: 14px; // 遮罩高度
    $headerHeight: 344px; // 头部高度

    .top_banner_btn {
        position: fixed;
        width: 100%;
        height: 200px;
        background-color: red;
    }
    .pages--loading {
        min-height: 100vh;
        background-color: #f8f8fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .pople {
        min-height: 100vh;
        background: #f8f8fa;
        overflow-wrap: break-word;
    }

    $downloadfixedHeight: 120px;
    // 下载 固定页面
    .pople__downloadfixed {
        position: fixed;
        z-index: 999;
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: $downloadfixedHeight;
        background: #F2F2F2;
        .pople__downloadfixed__img {
            width: 100%;
            height: 100%;
            object-fit: cover; // 图片自适应
        }
        .downloadfixed__left__container {
            display: flex;
            margin-left: 28px;
            margin-top: 16px;
            margin-bottom: 16px;
            .downloadfixed__left__logoimage {
                width: 88px;
                height: 88px;
                // background: #D8D8D8;
                border-radius: 8px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .downloadfixed__left__texts {
                flex: 1;
                margin-left: 24px;
            }
            .downloadfixed__left__title {
                margin-top: 6px;
    
                font-family: Inter-Regular-Bold, Inter-Regular;
                font-weight: normal;
                font-size: 32px;
                color: #252B37;

                // 超出1行，显示...
                display: -webkit-box;
                white-space: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }
            .downloadfixed__left__content {
                margin-top: 4px;
    
                // font-family: Inter, Inter;
                font-weight: 400;
                font-size: 24px;
                color: #252B37;

                // 超出1行，显示...
                display: -webkit-box;
                white-space: normal;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }
        }
        .downloadfixed__right__container {
            margin-top: 16px;
            margin-right: 28px;
            margin-left: 2px;
            .downloadfixed__right__button { 
                margin-top: 12px;
                display: flex;
                align-items: center;
                justify-content: center;

                width: 160px;
                height: 64px;
                background: linear-gradient(#FE714D 0%, #FFBB59 100%);
                border-radius: 16px;
                text-align: center;
                color: #F8F8FA;
            }
            .downloadfixed__right__text{
                font-family: Inter-Regular_Semi, Inter-Regular_Semi;
                font-weight: bold;
                font-size: 24px;
                color: inherit; // 继承父类
             }
        }
    }
    // 占据一个div的高度
    .pople__downloadfixed__defaultheight {
        width: 100%;
        height: $downloadfixedHeight;
    }

    $headerHeight: 344px;
    .pople__topbanner {
        height: $headerHeight;
    }
    // ----  顶部 ----
    .pople__header {
        position: relative;
        background: linear-gradient(
            180deg,
            rgba(255, 251, 243, 0) 0%,
            rgba(255, 251, 243, 0.6) 39%,
            #fbf8f2 100%
        );

        .header__background {
            width: 100%;
            height: $headerHeight;
            img {
                width: 100%;
                height: 100%;
                // background-size: 100% auto !important;
                background-size: auto 110% !important;
            }
        }

        .header__config {
            position: absolute;
            z-index: 3;
            top: 156px;
            left: 40px;
            display: flex;
            color: red;
            align-items: center;
        }

        .header__img {
            // width: calc(140px * 1);
            // height: calc(140px * 1);
            width: calc(120px * 1);
            height: calc(168px * 1);
            box-shadow: 0 8px 8px 0px rgba(59, 61, 65, 0.1);
            border-radius: 16px;
            flex: 0 0 auto;

            img {
                width: 100%;
                height: 100%;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                border-radius: 16px;
            }
        }

        .header__text {
            width: auto;
            margin-left: 28px;
            // width: 356px;
            height: 120px;
            font-size: 40px;
            font-weight: bold;
            color: #252b37;
            line-height: 60px;
            font-family: Inter-Regular-Bold, Inter-Regular;

            // 超出俩行，显示...
            display: -webkit-box;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        // 渐变遮罩
        .header__mask {
            position: absolute;
            z-index: 2;
            top: 0;
            left: 0;
            width: 100%;
            height: calc(344px + #{$maskHeight}); // + 的px是因为 linear 原因
            background: linear-gradient(
                180deg,
                rgba(255, 251, 243, 0) 0%,
                rgba(255, 251, 243, 0.6) 39%,
                #fbf8f2 100%
            );
        }
    }

    .pople__default__introduction {
        margin-left: 42px;
        margin-right: 42px;


        font-weight: normal;
        font-size: 32px;
        color: #9D6A2E;
        font-family: Charter Roman, Charter Roman;
    }

    // 内容引言 - 样式1
    .pople__introduction {
        padding-top: 58px;
        position: relative;
        
        .pople__introduction__leftmodel,
        .pople__introduction__rightmodel {
            // font-size: calc(54px + 10px);
            // font-size: calc(1em + 10px); // 1em = 当前父元素的font-size，再加10px
            font-size: calc(1em + 1em); // 1em = 当前父元素的font-size，再加10px
            // color: #D5BD86!important;
            font-family: DMSerifDisplay, DMSerifDisplay;
        }
        // 作为一个整体
        .pople__introduction__leftmodel,
        .pople__introduction__contentmodel {
            display: inline;
            line-height: 1;
        }
        // 右引号
        .pople__introduction__rightmodel {
            position: absolute;
            display: inline-block;
            // right: 42px; // 对应父元素的右padding
            right: 0; // 对应父元素的右padding
            // background-color: red;
        }
    }

    .pople__publicimage__introduction--0 {
        background-image: url("../img/img_public/左装饰@2x.png");
        background-size: 100% 100% !important;
        width: 88px;
        height: 88px;
        position: absolute;
    }
    .pople__publicimage__introduction--1 {
        left: 0;
        top: 0;
        transform: rotate(0deg);
    }
    .pople__publicimage__introduction--2 {
        right: 0;
        bottom: 0;
        transform: rotate(90deg);
    }
    .pople__publicimage__introduction--3 {
        left: 0;
        bottom: 0;
        transform: rotate(270deg);
    }
    .pople__publicimage__introduction--4 {
        right: 0;
        bottom: 0;
        transform: rotate(180deg);
    }
    

    // 内容引言 - 样式2
    .pople__2introduction {
        margin-top: 64px;

        position: relative;        // background-size: 100% 100% !important;
        // padding-top: 64px;
        // padding: 20px 40px;
        .pople__2introduction__contentmodel {
            padding: 20px 40px;
            min-height: 60px;
        }
        .pople__publicimage__introduction--1 {
        }
        .pople__publicimage__introduction--4 {
        }
    }

    // 内容引言 - 样式3
    .pople__3introduction {
        margin-top: 56px;
        position: relative;

        // padding: 12px;
        background: #F9F1E2;
        border-radius: 16px;
        .pople__3introduction__contentmodel {
            padding: 36px 56px;
            min-height: 160px;
        }
        .pople__publicimage__introduction--1 {
            left: 12px;
            top: 12px;
        }
        .pople__publicimage__introduction--2 {
            right: 12px;
            top: 12px;
        }
        .pople__publicimage__introduction--3 {
            left: 12px;
            bottom: 12px;
        }
        .pople__publicimage__introduction--4 {
            right: 12px;
            bottom: 12px;
        }
    }

    
    

    

    // 主题2 主题3 图片
    .pople__banner__container {
        // 不限制高度 等宽显示图片
        /**
        下面这样设置后:
            图片会保持原始宽高比
            宽度会填满容器(100%)
            高度会根据图片比例自动调整
            不会变形或被裁剪
            如果还需要其他调整,请告诉我
         */
        width: 100%;
        height: auto; // 改为auto让高度自适应  让高度根据图片原始比例自动计算
        object-fit: contain; // 保持图片比例  确保图片保持原始宽高比,完整显示
        display: block; // 避免图片底部留白  避免图片作为行内元素时底部可能出现的间隙
    }
    .pople__banner__container-them2 {
        padding-top: 0px;
    }
    .pople__banner__container-them3 {
        padding-top: 64px;
    }

    // 书本名
    .pople__bookname {
        padding-top: 56px;
        padding-left: 42px;
        padding-right: 42px;

        font-family: Inter, Inter;
        font-weight: bold;
        font-size: 48px;
        color: #252B37;

        // 超出俩行，显示...
        display: -webkit-box;
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .pople__bookname--them2 {
        padding-top: 56px;
    }
    .pople__bookname--them3 {
        padding-top: 40px;
    }
    

    //  ---- 小说 内容 ---
    .pople__content {
        // padding: calc(13px + #{$maskHeight}) 42px 23px;
        padding: calc(13px + #{$maskHeight}) 42px 0px; // 上 - 左右 - 下


        // 文字自动换行
        .text__autowrap {
            overflow-wrap: break-word;
        }

        // 富文本1
        .content__ritchtext1 {
        }

        // 主要内容
        .content__body {
            margin: 32px 0;
        }


        // 富文本2
        .content__ritchtext2 {
        }

        .pople__html {
            margin-top: 27px;
            // margin-bottom: 20px;
            text-align: center;
            text-decoration: underline; // 下划线
        }
        .pople__img {
            margin-top: 27px;
            // margin-bottom: 20px;
            img {
                width: 100%;
                height: 100%;
            }
        }

        .sizebox {
            // 这个高度由js动态设置
            // height: $popImageHeight;
        }

        .content__title {
            min-height: 67px;
            font-size: 42px;
            font-family: Inter-Regular-Bold, Inter-Regular;
            font-weight: 600;
            color: #252b37;
            line-height: 67px;
            margin-bottom: 42px;
        }

        .content__tgbanner {
            position: relative;
            z-index: 99999;
            margin-bottom: 42px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .content__canvas {
        }

        .content__chapterName {
            font-size: 36px;
            font-family: Inter-Regular_Semi-Bold, Inter-Regular_Semi;
            font-weight: bold;
            color: #252B37;
            line-height: 1.5;
            margin-bottom: 38px;
        }

        .content__book-content {
            .content__content-item {
                font-size: 33px;
                // font-family: Poppins-Regular, Inter-Regular;
                font-family: Georgia;
                font-weight: 400;
                color: #252b37;
                // line-height: 54px;
                margin: 20px 0;
                line-height: 1.5;
            }
        }
    }

    //  ----- 底部 ----
    .pople__download__box {
        padding: 0 42px;
        position: sticky; // 如果是 fixed 不会根据小说的内容的高度变化而变化
        bottom: 0;
        // height: 183px;
        text-align: center;
        // background: #f8f8fa;
        background-color: #f8f8fa;

        // border: 1px solid transparent; // 加这个是因为如果加上面的背景会有边框颜色不知道为什么

        // 滚动到底部显示的东西
        .pople__bubble {
            position: absolute;
            left: calc(50% - (680px / 2));
            bottom: 100%; //  这样才能根据底部的 提示 文案的高度变化而自动变化
            width: 680px;
            margin-bottom: 13px;
            // height: $popImageHeight;

            img {
                width: 100%;
                height: 100%;
                background-size: 100% 100% !important;
                // background-size: 100% auto  !important;
            }
        }

        .pople__bubble--html {
            height: auto;
        }
        .pople__bubble--img {
            height: $popImageHeight;
        }

        

        
        .pople__download__container {
            position: relative;
            // border: 1px solid transparent; // 这里是解决 sheen 动画的时候，这里会有边框
        }

        .pople__download__button {
            position: relative;
            margin-top: 20px;
            // overflow: hidden; // 如果增加这个，手指就会被隐藏掉一部分了w w

            width: 100%;
            height: 104px;
            line-height: 104px;

            // 默认
            font-size: 36px;
            color: #ffffff;
            font-family: Inter-Regular-Bold, Inter-Regular;
            font-weight: 700;
        }

        @keyframes sheen {
            100% {
                transform: rotateZ(60deg) translate(1em, -9em);
            }
        }

        /* 定义动画关键帧 */
        @keyframes pulse {
            0% {
                transform: scale(0.95);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(0.95);
            }
        }
        // 放大缩小动画
        .pople__download__button--animiatoin__pulse {
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes glow {
            50% {
                // box-shadow: 0 0 20px hsl(12, 100%, 60%);
                // box-shadow: 0 0 20px rgb(254, 30, 83);
                // box-shadow: 0 0 20px rgb(255, 121, 158);
                // box-shadow: 0 0 20px linear-gradient(180deg, #e8e8fe, #fe5450);
                box-shadow: 0 0 20px var(--emphasize-box-shadow-color);
            }
        }
        // 发光按钮
        .pople__download__button--animiatoin__emphasize {
            // --ease-elastic-in-1: cubic-bezier(.5,-0.25,.75,1);
            // animation: glow var(--ease-elastic-in-1) 600ms infinite alternate;
            // animation: glow cubic-bezier(.5,-0.25,.75,1) 600ms infinite alternate;
            animation: glow cubic-bezier(.5,-0.4,.75,1) 1000ms infinite alternate;
            // box-shadow: 0 0 20px rgb(255, 121, 158);
            // box-shadow: 0 0 20px linear-gradient(180deg, #e8e8fe, #fe5450);
            // box-shadow: 0 0 40px var(--emphasize-box-shadow-color);
        }

      

        .pople__download__html {
            position: relative;
            font-family: Inter-Regular-Bold, Inter-Regular;
            text-align: center;
            -webkit-text-size-adjust: 100% !important;
            word-break: break-word;

            overflow: hidden;
        }

        .pople__download__webp {
            position: absolute;
            right: 0px;
            top: 0;
            // bottom: -62px;
            // width: 203px;
            // width: 193px;
            // height: 193px;
            // width: 193px;
            // width: 153px;
            width: 193px;
            height: 153px;
            overflow: hidden;
        }

        .pople__download__button--img {
            img {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
            }
        }

        .pople__download__button--default {
            background: linear-gradient(180deg, #ffbf71, #fe5450);
            border-radius: 100px;
            // font-size: 38px;
            // font-family: Inter-Regular-Bold, Inter-Regular;
            // font-weight: 700;
            // color: #ffffff;
            // line-height: 102px;
            // text-align: center;
            // -webkit-text-size-adjust: 100% !important;
            // word-break: break-word;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
            white-space: nowrap;
        }

        .pople__download__tip {
            padding: 12px 0;
            font-size: 24px;
            font-family: Inter-Regular;
            color: #252B37;
        }
    }
</style>
